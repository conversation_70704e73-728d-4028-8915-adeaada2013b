[gd_scene load_steps=10 format=3 uid="uid://26v8vrtj6g87"]

[ext_resource type="Script" uid="uid://elouwbwc4cgs" path="res://Level Design/DemoScene.gd" id="1_7ldx6"]
[ext_resource type="Script" uid="uid://cf228t3rxn6tb" path="res://addons/SimpleDungeons/DungeonGenerator3D.gd" id="1_wd02n"]
[ext_resource type="Script" uid="uid://cpkogdyx2hnnb" path="res://Level Design/CamOrbitCenter.gd" id="4_huxu7"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_qyqmu"]
sky_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)
ground_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)

[sub_resource type="Sky" id="Sky_dscqc"]
sky_material = SubResource("ProceduralSkyMaterial_qyqmu")

[sub_resource type="Environment" id="Environment_4mo8u"]
background_mode = 2
sky = SubResource("Sky_dscqc")
ssr_enabled = true
ssao_enabled = true
sdfgi_use_occlusion = true
sdfgi_min_cell_size = 0.00976562
sdfgi_cascade0_distance = 0.625
sdfgi_max_distance = 10.0
fog_light_color = Color(0.250705, 0.218236, 0.327787, 1)
fog_density = 0.02

[sub_resource type="CameraAttributesPhysical" id="CameraAttributesPhysical_y72vu"]
auto_exposure_enabled = true

[sub_resource type="GDScript" id="GDScript_fku1n"]
script/source = "@tool
extends Panel

func _process(_delta):
	self.size = $\"../MarginContainer\".size
	self.position = $\"../MarginContainer\".position
"

[sub_resource type="Theme" id="Theme_am664"]

[node name="DemoScene" type="Node3D"]
script = ExtResource("1_7ldx6")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
shadow_enabled = true

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_4mo8u")
camera_attributes = SubResource("CameraAttributesPhysical_y72vu")

[node name="DungeonGenerator3D" type="Node3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 0)
script = ExtResource("1_wd02n")
generate_on_ready = false
heuristic_scale = 3.0

[node name="GUI" type="Control" parent="."]
unique_name_in_owner = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -960.0
offset_bottom = -540.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="BGPanel" type="Panel" parent="GUI"]
layout_mode = 0
offset_left = 659.0
offset_top = 15.0
offset_right = 945.0
offset_bottom = 443.0
script = SubResource("GDScript_fku1n")

[node name="MarginContainer" type="MarginContainer" parent="GUI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -292.0
offset_top = 15.0
offset_right = -15.0
offset_bottom = 373.0
grow_horizontal = 0
theme = SubResource("Theme_am664")
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="GUI/MarginContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="GUI/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Dungeon Kit"

[node name="OptionButtonDungeons" type="OptionButton" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0

[node name="Label5" type="Label" parent="GUI/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Dungeon Size"

[node name="HBoxContainer" type="HBoxContainer" parent="GUI/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="X" type="SpinBox" parent="GUI/MarginContainer/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
value = 10.0

[node name="Y" type="SpinBox" parent="GUI/MarginContainer/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
value = 10.0

[node name="Z" type="SpinBox" parent="GUI/MarginContainer/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
value = 10.0

[node name="Label3" type="Label" parent="GUI/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Generation Seed"

[node name="Seed" type="SpinBox" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
max_value = 9.22337e+18
value = 10.0

[node name="Label4" type="Label" parent="GUI/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Visualization Delay Per Iteration"

[node name="WaitMsRange" type="SpinBox" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
max_value = 10000.0
value = 100.0
suffix = "ms"

[node name="AutoRotateCheckBox" type="CheckBox" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "Auto rotate"

[node name="ShowDebugCheckBox" type="CheckBox" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "Show debug grid"

[node name="RegenerateButton" type="Button" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "Generate"

[node name="GenerateWithNewSeedButton" type="Button" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "Generate With New Seed"

[node name="SpawnPlayerButton" type="Button" parent="GUI/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "Spawn Player"

[node name="CamOrbitCenter" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("4_huxu7")

[node name="Camera3D" type="Camera3D" parent="CamOrbitCenter"]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 152)
keep_aspect = 0

[connection signal="pressed" from="GUI/MarginContainer/VBoxContainer/RegenerateButton" to="." method="regenerate"]
[connection signal="pressed" from="GUI/MarginContainer/VBoxContainer/GenerateWithNewSeedButton" to="." method="_on_generate_with_new_seed_button_pressed"]
[connection signal="pressed" from="GUI/MarginContainer/VBoxContainer/SpawnPlayerButton" to="." method="_on_spawn_player_button_pressed"]
