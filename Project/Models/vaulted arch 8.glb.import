[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://xgahq2g7ls66"
path="res://.godot/imported/vaulted arch 8.glb-5f87c504469561325743e7daf9cecbdc.scn"

[deps]

source_file="res://Project/Models/vaulted arch 8.glb"
dest_files=["res://.godot/imported/vaulted arch 8.glb-5f87c504469561325743e7daf9cecbdc.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=2
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={
"nodes": {
"PATH:room box": {
"generate/physics": true,
"physics/layer": 3
}
}
}
gltf/naming_version=1
gltf/embedded_image_handling=1
