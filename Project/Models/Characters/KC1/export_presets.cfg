[preset.0]

name="Windows Desktop"
platform="Windows Desktop"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="scenes"
export_files=PackedStringArray("res://Project 2024/Scenes/Shop/Lightmaps/shop_light_map_gi_settings.tscn", "res://Project 2024/Scenes/Shop/Shop.tscn")
include_filter=""
exclude_filter=""
export_path="Builds/Windows/LUCID_liminal.exe"
patches=PackedStringArray()
encryption_include_filters=""
encryption_exclude_filters=""
seed=0
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.0.options]

custom_template/debug=""
custom_template/release=""
debug/export_console_wrapper=0
binary_format/embed_pck=false
texture_format/s3tc_bptc=true
texture_format/etc2_astc=false
binary_format/architecture="x86_64"
codesign/enable=false
codesign/timestamp=true
codesign/timestamp_server_url=""
codesign/digest_algorithm=1
codesign/description=""
codesign/custom_options=PackedStringArray()
application/modify_resources=true
application/icon=""
application/console_wrapper_icon=""
application/icon_interpolation=4
application/file_version=""
application/product_version=""
application/company_name=""
application/product_name=""
application/file_description=""
application/copyright=""
application/trademarks=""
application/export_angle=0
application/export_d3d12=1
application/d3d12_agility_sdk_multiarch=true
ssh_remote_deploy/enabled=false
ssh_remote_deploy/host="user@host_ip"
ssh_remote_deploy/port="22"
ssh_remote_deploy/extra_args_ssh=""
ssh_remote_deploy/extra_args_scp=""
ssh_remote_deploy/run_script="Expand-Archive -LiteralPath '{temp_dir}\\{archive_name}' -DestinationPath '{temp_dir}'
$action = New-ScheduledTaskAction -Execute '{temp_dir}\\{exe_name}' -Argument '{cmd_args}'
$trigger = New-ScheduledTaskTrigger -Once -At 00:00
$settings = New-ScheduledTaskSettingsSet
$task = New-ScheduledTask -Action $action -Trigger $trigger -Settings $settings
Register-ScheduledTask godot_remote_debug -InputObject $task -Force:$true
Start-ScheduledTask -TaskName godot_remote_debug
while (Get-ScheduledTask -TaskName godot_remote_debug | ? State -eq running) { Start-Sleep -Milliseconds 100 }
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue"
ssh_remote_deploy/cleanup_script="Stop-ScheduledTask -TaskName godot_remote_debug -ErrorAction:SilentlyContinue
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue
Remove-Item -Recurse -Force '{temp_dir}'"
dotnet/include_scripts_content=false
dotnet/include_debug_symbols=false
dotnet/embed_build_outputs=false

[preset.1]

name="Web"
platform="Web"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="scenes"
export_files=PackedStringArray("res://Project 2024/Scenes/Tests/mesh_root.tscn", "res://Project 2024/Scenes/Tests/Interactor.tscn", "res://Project 2024/Scenes/Tests/Interactable.tscn", "res://Project 2024/Scenes/Shop/Shop.tscn", "res://Project 2024/Scenes/Shop/Lightmaps/shop_light_map_gi_settings.tscn", "res://Project 2024/Models/Scenes/Shop/Light Rays.glb", "res://Project 2024/Models/Scenes/Shop/01_Cowbow_Deep.glb", "res://Project/UI/CombatUI.tscn", "res://Project/UI/command.tscn", "res://Project/UI/held_slot.tscn", "res://Project/UI/InventoryUI.tscn", "res://Project/UI/Main Menu.tscn", "res://Project/UI/PauseMenu.tscn", "res://Project/UI/Selector.tscn", "res://Project/UI/SMS UI.tscn", "res://Project/Systems/Interaction/PlayerInteractor.tscn", "res://Project/Systems/Third Person Movement/Player/sword.glb", "res://Project/Systems/Third Person Movement/Player/sword.tscn", "res://Project/Prefabs/star_ui.tscn", "res://Project/Models/Characters/KC1/elf shirt/elf shirt.fbx", "res://Project/Models/Characters/KC1/KC1.vrm", "res://addons/dialogic/Editor/CharacterEditor/character_editor.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_p_section_exports.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_p_section_layout.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_p_section_main.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_p_section_main_exports.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_section_general.tscn", "res://addons/dialogic/Editor/CharacterEditor/char_edit_section_portraits.tscn", "res://addons/dialogic/Editor/CharacterEditor/portrait_scene_browser.tscn", "res://addons/dialogic/Editor/Common/BrowserItem.tscn", "res://addons/dialogic/Editor/Common/hint_tooltip_icon.tscn", "res://addons/dialogic/Editor/Common/reference_manager.tscn", "res://addons/dialogic/Editor/Common/side_bar.tscn", "res://addons/dialogic/Editor/Common/update_install_window.tscn", "res://addons/dialogic/Editor/Events/EventBlock/event_block.tscn", "res://addons/dialogic/Editor/Events/Fields/array_part.tscn", "res://addons/dialogic/Editor/Events/Fields/dictionary_part.tscn", "res://addons/dialogic/Editor/Events/Fields/field_array.tscn", "res://addons/dialogic/Editor/Events/Fields/field_audio_preview.tscn", "res://addons/dialogic/Editor/Events/Fields/field_bool_button.tscn", "res://addons/dialogic/Editor/Events/Fields/field_bool_check.tscn", "res://addons/dialogic/Editor/Events/Fields/field_color.tscn", "res://addons/dialogic/Editor/Events/Fields/field_condition.tscn", "res://addons/dialogic/Editor/Events/Fields/field_dictionary.tscn", "res://addons/dialogic/Editor/Events/Fields/field_file.tscn", "res://addons/dialogic/Editor/Events/Fields/field_flex_value.tscn", "res://addons/dialogic/Editor/Events/Fields/field_number.tscn", "res://addons/dialogic/Editor/Events/Fields/field_options_dynamic.tscn", "res://addons/dialogic/Editor/Events/Fields/field_options_fixed.tscn", "res://addons/dialogic/Editor/Events/Fields/field_text_multiline.tscn", "res://addons/dialogic/Editor/Events/Fields/field_text_singleline.tscn", "res://addons/dialogic/Editor/Events/Fields/field_vector2.tscn", "res://addons/dialogic/Editor/Events/Fields/field_vector3.tscn", "res://addons/dialogic/Editor/Events/Fields/field_vector4.tscn", "res://addons/dialogic/Editor/Events/BranchEnd.tscn", "res://addons/dialogic/Editor/HomePage/home_page.tscn", "res://addons/dialogic/Editor/Settings/settings_editor.tscn", "res://addons/dialogic/Editor/Settings/settings_general.tscn", "res://addons/dialogic/Editor/Settings/settings_modules.tscn", "res://addons/dialogic/Editor/Settings/settings_translation.tscn", "res://addons/dialogic/Editor/TimelineEditor/TextEditor/timeline_editor_text.tscn", "res://addons/dialogic/Editor/TimelineEditor/VisualEditor/AddEventButton.tscn", "res://addons/dialogic/Editor/TimelineEditor/VisualEditor/timeline_editor_visual.tscn", "res://addons/dialogic/Editor/TimelineEditor/test_timeline_scene.tscn", "res://addons/dialogic/Editor/TimelineEditor/timeline_editor.tscn", "res://addons/dialogic/Editor/editor_main.tscn", "res://addons/dialogic/Example Assets/portraits/CustomPortrait_AnimatedSprite.tscn", "res://addons/dialogic/Example Assets/portraits/CustomPortrait_FaceAtlas.tscn", "res://addons/dialogic/Modules/Audio/settings_audio.tscn", "res://addons/dialogic/Modules/Background/DefaultBackgroundScene/default_background.tscn", "res://addons/dialogic/Modules/Character/default_portrait.tscn", "res://addons/dialogic/Modules/Character/settings_portraits.tscn", "res://addons/dialogic/Modules/Choice/settings_choices.tscn", "res://addons/dialogic/Modules/Choice/ui_choice_end.tscn", "res://addons/dialogic/Modules/Condition/ui_condition_end.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Base_Default/default_layout_base.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Base_TextBubble/text_bubble_base.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_FullBackground/full_background_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Glossary/glossary_popup_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_History/example_history_item.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_History/history_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Input/full_advance_input_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_SpeakerPortraitTextbox/textbox_with_speaker_portrait.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Textbubble/text_bubble.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Textbubble/text_bubble_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_TextInput/text_input_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Choices/vn_choice_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Portraits/vn_portrait_layer.tscn", "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_layer.tscn", "res://addons/dialogic/Modules/Glossary/glossary_editor.tscn", "res://addons/dialogic/Modules/HighlightPortrait/simple_highlight_portrait.tscn", "res://addons/dialogic/Modules/History/settings_history.tscn", "res://addons/dialogic/Modules/LayeredPortrait/layered_portrait.tscn", "res://addons/dialogic/Modules/Save/settings_save.tscn", "res://addons/dialogic/Modules/Style/character_settings_style.tscn", "res://addons/dialogic/Modules/StyleEditor/Components/style_browser.tscn", "res://addons/dialogic/Modules/StyleEditor/style_editor.tscn", "res://addons/dialogic/Modules/Text/character_settings/character_moods_settings.tscn", "res://addons/dialogic/Modules/Text/character_settings/character_portrait_mood_settings.tscn", "res://addons/dialogic/Modules/Text/settings_text.tscn", "res://addons/dialogic/Modules/Variable/variables_editor/variables_editor.tscn")
include_filter=""
exclude_filter=""
export_path="Builds/web/LUCID_liminal.html"
patches=PackedStringArray()
encryption_include_filters=""
encryption_exclude_filters=""
seed=0
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.1.options]

custom_template/debug=""
custom_template/release=""
variant/extensions_support=true
variant/thread_support=false
vram_texture_compression/for_desktop=true
vram_texture_compression/for_mobile=true
html/export_icon=true
html/custom_html_shell=""
html/head_include=""
html/canvas_resize_policy=2
html/focus_canvas_on_start=true
html/experimental_virtual_keyboard=false
progressive_web_app/enabled=false
progressive_web_app/ensure_cross_origin_isolation_headers=true
progressive_web_app/offline_page=""
progressive_web_app/display=1
progressive_web_app/orientation=0
progressive_web_app/icon_144x144=""
progressive_web_app/icon_180x180=""
progressive_web_app/icon_512x512=""
progressive_web_app/background_color=Color(0, 0, 0, 1)
dotnet/include_scripts_content=false
dotnet/include_debug_symbols=true
dotnet/embed_build_outputs=false

[preset.2]

name="macOS"
platform="macOS"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="Builds/LUCID_liminal.app"
patches=PackedStringArray()
encryption_include_filters=""
encryption_exclude_filters=""
seed=0
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.2.options]

export/distribution_type=0
binary_format/architecture="universal"
custom_template/debug=""
custom_template/release=""
debug/export_console_wrapper=1
application/icon=""
application/icon_interpolation=4
application/bundle_identifier="com.yikescg.angelnight"
application/signature=""
application/app_category="Games"
application/short_version=""
application/version=""
application/copyright=""
application/copyright_localized={}
application/min_macos_version_x86_64="10.12"
application/min_macos_version_arm64="11.00"
application/export_angle=0
display/high_res=true
application/additional_plist_content=""
xcode/platform_build="14C18"
xcode/sdk_version="13.1"
xcode/sdk_build="22C55"
xcode/sdk_name="macosx13.1"
xcode/xcode_version="1420"
xcode/xcode_build="14C18"
codesign/codesign=3
codesign/installer_identity=""
codesign/apple_team_id=""
codesign/identity=""
codesign/entitlements/custom_file=""
codesign/entitlements/allow_jit_code_execution=false
codesign/entitlements/allow_unsigned_executable_memory=false
codesign/entitlements/allow_dyld_environment_variables=false
codesign/entitlements/disable_library_validation=false
codesign/entitlements/audio_input=false
codesign/entitlements/camera=false
codesign/entitlements/location=false
codesign/entitlements/address_book=false
codesign/entitlements/calendars=false
codesign/entitlements/photos_library=false
codesign/entitlements/apple_events=false
codesign/entitlements/debugging=true
codesign/entitlements/app_sandbox/enabled=false
codesign/entitlements/app_sandbox/network_server=false
codesign/entitlements/app_sandbox/network_client=false
codesign/entitlements/app_sandbox/device_usb=false
codesign/entitlements/app_sandbox/device_bluetooth=false
codesign/entitlements/app_sandbox/files_downloads=0
codesign/entitlements/app_sandbox/files_pictures=0
codesign/entitlements/app_sandbox/files_music=0
codesign/entitlements/app_sandbox/files_movies=0
codesign/entitlements/app_sandbox/files_user_selected=0
codesign/entitlements/app_sandbox/helper_executables=[]
codesign/entitlements/additional=""
codesign/custom_options=PackedStringArray()
notarization/notarization=0
privacy/microphone_usage_description=""
privacy/microphone_usage_description_localized={}
privacy/camera_usage_description=""
privacy/camera_usage_description_localized={}
privacy/location_usage_description=""
privacy/location_usage_description_localized={}
privacy/address_book_usage_description=""
privacy/address_book_usage_description_localized={}
privacy/calendar_usage_description=""
privacy/calendar_usage_description_localized={}
privacy/photos_library_usage_description=""
privacy/photos_library_usage_description_localized={}
privacy/desktop_folder_usage_description=""
privacy/desktop_folder_usage_description_localized={}
privacy/documents_folder_usage_description=""
privacy/documents_folder_usage_description_localized={}
privacy/downloads_folder_usage_description=""
privacy/downloads_folder_usage_description_localized={}
privacy/network_volumes_usage_description=""
privacy/network_volumes_usage_description_localized={}
privacy/removable_volumes_usage_description=""
privacy/removable_volumes_usage_description_localized={}
privacy/tracking_enabled=false
privacy/tracking_domains=PackedStringArray()
privacy/collected_data/name/collected=false
privacy/collected_data/name/linked_to_user=false
privacy/collected_data/name/used_for_tracking=false
privacy/collected_data/name/collection_purposes=0
privacy/collected_data/email_address/collected=false
privacy/collected_data/email_address/linked_to_user=false
privacy/collected_data/email_address/used_for_tracking=false
privacy/collected_data/email_address/collection_purposes=0
privacy/collected_data/phone_number/collected=false
privacy/collected_data/phone_number/linked_to_user=false
privacy/collected_data/phone_number/used_for_tracking=false
privacy/collected_data/phone_number/collection_purposes=0
privacy/collected_data/physical_address/collected=false
privacy/collected_data/physical_address/linked_to_user=false
privacy/collected_data/physical_address/used_for_tracking=false
privacy/collected_data/physical_address/collection_purposes=0
privacy/collected_data/other_contact_info/collected=false
privacy/collected_data/other_contact_info/linked_to_user=false
privacy/collected_data/other_contact_info/used_for_tracking=false
privacy/collected_data/other_contact_info/collection_purposes=0
privacy/collected_data/health/collected=false
privacy/collected_data/health/linked_to_user=false
privacy/collected_data/health/used_for_tracking=false
privacy/collected_data/health/collection_purposes=0
privacy/collected_data/fitness/collected=false
privacy/collected_data/fitness/linked_to_user=false
privacy/collected_data/fitness/used_for_tracking=false
privacy/collected_data/fitness/collection_purposes=0
privacy/collected_data/payment_info/collected=false
privacy/collected_data/payment_info/linked_to_user=false
privacy/collected_data/payment_info/used_for_tracking=false
privacy/collected_data/payment_info/collection_purposes=0
privacy/collected_data/credit_info/collected=false
privacy/collected_data/credit_info/linked_to_user=false
privacy/collected_data/credit_info/used_for_tracking=false
privacy/collected_data/credit_info/collection_purposes=0
privacy/collected_data/other_financial_info/collected=false
privacy/collected_data/other_financial_info/linked_to_user=false
privacy/collected_data/other_financial_info/used_for_tracking=false
privacy/collected_data/other_financial_info/collection_purposes=0
privacy/collected_data/precise_location/collected=false
privacy/collected_data/precise_location/linked_to_user=false
privacy/collected_data/precise_location/used_for_tracking=false
privacy/collected_data/precise_location/collection_purposes=0
privacy/collected_data/coarse_location/collected=false
privacy/collected_data/coarse_location/linked_to_user=false
privacy/collected_data/coarse_location/used_for_tracking=false
privacy/collected_data/coarse_location/collection_purposes=0
privacy/collected_data/sensitive_info/collected=false
privacy/collected_data/sensitive_info/linked_to_user=false
privacy/collected_data/sensitive_info/used_for_tracking=false
privacy/collected_data/sensitive_info/collection_purposes=0
privacy/collected_data/contacts/collected=false
privacy/collected_data/contacts/linked_to_user=false
privacy/collected_data/contacts/used_for_tracking=false
privacy/collected_data/contacts/collection_purposes=0
privacy/collected_data/emails_or_text_messages/collected=false
privacy/collected_data/emails_or_text_messages/linked_to_user=false
privacy/collected_data/emails_or_text_messages/used_for_tracking=false
privacy/collected_data/emails_or_text_messages/collection_purposes=0
privacy/collected_data/photos_or_videos/collected=false
privacy/collected_data/photos_or_videos/linked_to_user=false
privacy/collected_data/photos_or_videos/used_for_tracking=false
privacy/collected_data/photos_or_videos/collection_purposes=0
privacy/collected_data/audio_data/collected=false
privacy/collected_data/audio_data/linked_to_user=false
privacy/collected_data/audio_data/used_for_tracking=false
privacy/collected_data/audio_data/collection_purposes=0
privacy/collected_data/gameplay_content/collected=false
privacy/collected_data/gameplay_content/linked_to_user=false
privacy/collected_data/gameplay_content/used_for_tracking=false
privacy/collected_data/gameplay_content/collection_purposes=0
privacy/collected_data/customer_support/collected=false
privacy/collected_data/customer_support/linked_to_user=false
privacy/collected_data/customer_support/used_for_tracking=false
privacy/collected_data/customer_support/collection_purposes=0
privacy/collected_data/other_user_content/collected=false
privacy/collected_data/other_user_content/linked_to_user=false
privacy/collected_data/other_user_content/used_for_tracking=false
privacy/collected_data/other_user_content/collection_purposes=0
privacy/collected_data/browsing_history/collected=false
privacy/collected_data/browsing_history/linked_to_user=false
privacy/collected_data/browsing_history/used_for_tracking=false
privacy/collected_data/browsing_history/collection_purposes=0
privacy/collected_data/search_hhistory/collected=false
privacy/collected_data/search_hhistory/linked_to_user=false
privacy/collected_data/search_hhistory/used_for_tracking=false
privacy/collected_data/search_hhistory/collection_purposes=0
privacy/collected_data/user_id/collected=false
privacy/collected_data/user_id/linked_to_user=false
privacy/collected_data/user_id/used_for_tracking=false
privacy/collected_data/user_id/collection_purposes=0
privacy/collected_data/device_id/collected=false
privacy/collected_data/device_id/linked_to_user=false
privacy/collected_data/device_id/used_for_tracking=false
privacy/collected_data/device_id/collection_purposes=0
privacy/collected_data/purchase_history/collected=false
privacy/collected_data/purchase_history/linked_to_user=false
privacy/collected_data/purchase_history/used_for_tracking=false
privacy/collected_data/purchase_history/collection_purposes=0
privacy/collected_data/product_interaction/collected=false
privacy/collected_data/product_interaction/linked_to_user=false
privacy/collected_data/product_interaction/used_for_tracking=false
privacy/collected_data/product_interaction/collection_purposes=0
privacy/collected_data/advertising_data/collected=false
privacy/collected_data/advertising_data/linked_to_user=false
privacy/collected_data/advertising_data/used_for_tracking=false
privacy/collected_data/advertising_data/collection_purposes=0
privacy/collected_data/other_usage_data/collected=false
privacy/collected_data/other_usage_data/linked_to_user=false
privacy/collected_data/other_usage_data/used_for_tracking=false
privacy/collected_data/other_usage_data/collection_purposes=0
privacy/collected_data/crash_data/collected=false
privacy/collected_data/crash_data/linked_to_user=false
privacy/collected_data/crash_data/used_for_tracking=false
privacy/collected_data/crash_data/collection_purposes=0
privacy/collected_data/performance_data/collected=false
privacy/collected_data/performance_data/linked_to_user=false
privacy/collected_data/performance_data/used_for_tracking=false
privacy/collected_data/performance_data/collection_purposes=0
privacy/collected_data/other_diagnostic_data/collected=false
privacy/collected_data/other_diagnostic_data/linked_to_user=false
privacy/collected_data/other_diagnostic_data/used_for_tracking=false
privacy/collected_data/other_diagnostic_data/collection_purposes=0
privacy/collected_data/environment_scanning/collected=false
privacy/collected_data/environment_scanning/linked_to_user=false
privacy/collected_data/environment_scanning/used_for_tracking=false
privacy/collected_data/environment_scanning/collection_purposes=0
privacy/collected_data/hands/collected=false
privacy/collected_data/hands/linked_to_user=false
privacy/collected_data/hands/used_for_tracking=false
privacy/collected_data/hands/collection_purposes=0
privacy/collected_data/head/collected=false
privacy/collected_data/head/linked_to_user=false
privacy/collected_data/head/used_for_tracking=false
privacy/collected_data/head/collection_purposes=0
privacy/collected_data/other_data_types/collected=false
privacy/collected_data/other_data_types/linked_to_user=false
privacy/collected_data/other_data_types/used_for_tracking=false
privacy/collected_data/other_data_types/collection_purposes=0
ssh_remote_deploy/enabled=false
ssh_remote_deploy/host="user@host_ip"
ssh_remote_deploy/port="22"
ssh_remote_deploy/extra_args_ssh=""
ssh_remote_deploy/extra_args_scp=""
ssh_remote_deploy/run_script="#!/usr/bin/env bash
unzip -o -q \"{temp_dir}/{archive_name}\" -d \"{temp_dir}\"
open \"{temp_dir}/{exe_name}.app\" --args {cmd_args}"
ssh_remote_deploy/cleanup_script="#!/usr/bin/env bash
kill $(pgrep -x -f \"{temp_dir}/{exe_name}.app/Contents/MacOS/{exe_name} {cmd_args}\")
rm -rf \"{temp_dir}\""
dotnet/include_scripts_content=false
dotnet/include_debug_symbols=true
dotnet/embed_build_outputs=false
