[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://b5c0p00x6g6u5"
path="res://.godot/imported/Roboto-Italic.ttf-844485a0171d6031f98f4829003a881a.fontdata"

[deps]

source_file="res://addons/dialogic/Example Assets/Fonts/Roboto-Italic.ttf"
dest_files=["res://.godot/imported/Roboto-Italic.ttf-844485a0171d6031f98f4829003a881a.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
