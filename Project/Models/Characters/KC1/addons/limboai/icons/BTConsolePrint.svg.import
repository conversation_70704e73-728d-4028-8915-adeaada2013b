[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d0lfqtw5n3kcw"
path="res://.godot/imported/BTConsolePrint.svg-4190426bb73da16c143878c233af4bc3.ctex"
metadata={
"has_editor_variant": true,
"vram_texture": false
}

[deps]

source_file="res://addons/limboai/icons/BTConsolePrint.svg"
dest_files=["res://.godot/imported/BTConsolePrint.svg-4190426bb73da16c143878c233af4bc3.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
svg/scale=1.0
editor/scale_with_editor_scale=true
editor/convert_colors_with_editor_theme=true
