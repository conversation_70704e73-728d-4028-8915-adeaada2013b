[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://bj6vcwb7gnu40"
path="res://.godot/imported/KC1.vrm-60cc0d3d620a8f5a579a9940fa533345.scn"

[deps]

source_file="res://Project/Models/Characters/KC1/KC1.vrm"
dest_files=["res://.godot/imported/KC1.vrm-60cc0d3d620a8f5a579a9940fa533345.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={
"materials": {
"N00_000_00_Body_00_SKIN (Instance)": {
"use_external/enabled": false,
"use_external/path": "res://Project/Models/Characters/KC1 Mats/N00_000_00_Body_00_SKIN (Instance).tres"
},
"N00_000_00_FaceMouth_00_FACE (Instance)": {
"use_external/enabled": false,
"use_external/path": "res://Project/Models/Characters/KC1 Mats/N00_000_00_FaceMouth_00_FACE (Instance).tres"
}
},
"nodes": {
"PATH:GeneralSkeleton": {
"retarget/bone_map": Object(BoneMap,"resource_local_to_scene":false,"resource_name":"","profile":Object(SkeletonProfileHumanoid,"resource_local_to_scene":false,"resource_name":"","root_bone":&"Root","scale_base_bone":&"Hips","group_size":4,"bone_size":56,"script":null)
,"bonemap":null,"bone_map/Root":&"Root_","bone_map/Hips":&"Hips","bone_map/Spine":&"Spine","bone_map/Chest":&"Chest","bone_map/UpperChest":&"UpperChest","bone_map/Neck":&"Neck","bone_map/Head":&"Head","bone_map/LeftEye":&"LeftEye","bone_map/RightEye":&"RightEye","bone_map/Jaw":&"","bone_map/LeftShoulder":&"LeftShoulder","bone_map/LeftUpperArm":&"LeftUpperArm","bone_map/LeftLowerArm":&"LeftLowerArm","bone_map/LeftHand":&"LeftHand","bone_map/LeftThumbMetacarpal":&"LeftThumbMetacarpal","bone_map/LeftThumbProximal":&"LeftThumbProximal","bone_map/LeftThumbDistal":&"LeftThumbDistal","bone_map/LeftIndexProximal":&"LeftIndexProximal","bone_map/LeftIndexIntermediate":&"LeftIndexIntermediate","bone_map/LeftIndexDistal":&"LeftIndexDistal","bone_map/LeftMiddleProximal":&"LeftMiddleProximal","bone_map/LeftMiddleIntermediate":&"LeftMiddleIntermediate","bone_map/LeftMiddleDistal":&"LeftMiddleDistal","bone_map/LeftRingProximal":&"LeftRingProximal","bone_map/LeftRingIntermediate":&"LeftRingIntermediate","bone_map/LeftRingDistal":&"LeftRingDistal","bone_map/LeftLittleProximal":&"LeftLittleProximal","bone_map/LeftLittleIntermediate":&"LeftLittleIntermediate","bone_map/LeftLittleDistal":&"LeftLittleDistal","bone_map/RightShoulder":&"RightShoulder","bone_map/RightUpperArm":&"RightUpperArm","bone_map/RightLowerArm":&"RightLowerArm","bone_map/RightHand":&"RightHand","bone_map/RightThumbMetacarpal":&"RightThumbMetacarpal","bone_map/RightThumbProximal":&"RightThumbProximal","bone_map/RightThumbDistal":&"RightThumbDistal","bone_map/RightIndexProximal":&"RightIndexProximal","bone_map/RightIndexIntermediate":&"RightIndexIntermediate","bone_map/RightIndexDistal":&"RightIndexDistal","bone_map/RightMiddleProximal":&"RightMiddleProximal","bone_map/RightMiddleIntermediate":&"RightMiddleIntermediate","bone_map/RightMiddleDistal":&"RightMiddleDistal","bone_map/RightRingProximal":&"RightRingProximal","bone_map/RightRingIntermediate":&"RightRingIntermediate","bone_map/RightRingDistal":&"RightRingDistal","bone_map/RightLittleProximal":&"RightLittleProximal","bone_map/RightLittleIntermediate":&"RightLittleIntermediate","bone_map/RightLittleDistal":&"RightLittleDistal","bone_map/LeftUpperLeg":&"LeftUpperLeg","bone_map/LeftLowerLeg":&"LeftLowerLeg","bone_map/LeftFoot":&"LeftFoot","bone_map/LeftToes":&"LeftToes","bone_map/RightUpperLeg":&"RightUpperLeg","bone_map/RightLowerLeg":&"RightLowerLeg","bone_map/RightFoot":&"RightFoot","bone_map/RightToes":&"RightToes","script":null)

}
}
}
vrm/head_hiding_method=0
vrm/only_if_head_hiding_uses_layers/first_person_layers=2
vrm/only_if_head_hiding_uses_layers/third_person_layers=4
