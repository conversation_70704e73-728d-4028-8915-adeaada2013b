[gd_resource type="ShaderMaterial" load_steps=8 format=3 uid="uid://d3rn857oru6xh"]

[ext_resource type="Shader" uid="uid://bjaf8xdxnakjw" path="res://addons/Godot-MToon-Shader/mtoon_cull_off.gdshader" id="1_8yos3"]
[ext_resource type="Texture2D" uid="uid://da1ryyvs81a1u" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Normal.png" id="2_1oabf"]
[ext_resource type="Texture2D" uid="uid://ig1qm0ee2ovl" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Emission.png" id="3_scljr"]
[ext_resource type="Texture2D" uid="uid://ditqqq7uto5ar" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Lit.png" id="4_p33vu"]
[ext_resource type="Texture2D" uid="uid://bo5rvxueqmtxy" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Outline.png" id="5_hpp1w"]
[ext_resource type="Texture2D" uid="uid://cylk1o47qe3nq" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Shade.png" id="6_djj7o"]
[ext_resource type="Texture2D" uid="uid://bm41slk8mah6f" path="res://Project/Models/Characters/KC1/KC1 Materials/Body/Matcap.png" id="7_33411"]

[resource]
resource_name = "N00_000_00_Body_00_SKIN (Instance)"
render_priority = 0
shader = ExtResource("1_8yos3")
shader_parameter/_AlphaCutoutEnable = 0.0
shader_parameter/_Cutoff = 0.5
shader_parameter/_Color = Color(1, 1, 1, 1)
shader_parameter/_ShadeColor = Color(0.466797, 0.380184, 0.459354, 1)
shader_parameter/_MainTex = ExtResource("4_p33vu")
shader_parameter/_MainTex_ST = Vector4(1, 1, 0, 0)
shader_parameter/_ShadeTexture = ExtResource("6_djj7o")
shader_parameter/_BumpScale = 0.0
shader_parameter/_BumpMap = ExtResource("2_1oabf")
shader_parameter/_ReceiveShadowRate = 1.0
shader_parameter/_ShadingGradeRate = 1.0
shader_parameter/_ShadeShift = -0.15
shader_parameter/_ShadeToony = 0.9
shader_parameter/_LightColorAttenuation = 1.0
shader_parameter/_IndirectLightIntensity = 0.0
shader_parameter/_RimColor = Color(0, 0, 0, 1)
shader_parameter/_RimLightingMix = 1.0
shader_parameter/_RimFresnelPower = 50.0
shader_parameter/_RimLift = 0.0
shader_parameter/_MatcapColor = Color(1, 1, 1, 1)
shader_parameter/_SphereAdd = ExtResource("7_33411")
shader_parameter/_EmissionColor = Color(1, 1, 1, 1)
shader_parameter/_EmissionMultiplier = 1.0
shader_parameter/_EmissionMap = ExtResource("3_scljr")
shader_parameter/_OutlineWidthMode = 0.0
shader_parameter/_OutlineWidthTexture = ExtResource("5_hpp1w")
shader_parameter/_OutlineWidth = 0.0
shader_parameter/_OutlineScaledMaxDistance = 1.0
shader_parameter/_OutlineColorMode = 0.0
shader_parameter/_OutlineColor = Color(0, 0, 0, 0)
shader_parameter/_OutlineLightingMix = 1.0
shader_parameter/_UvAnimScrollX = 0.0
shader_parameter/_UvAnimRotation = 0.0
shader_parameter/_UvAnimScrollY = 0.0
shader_parameter/_DebugMode = 0.0
shader_parameter/_MToonVersion = 34.0
