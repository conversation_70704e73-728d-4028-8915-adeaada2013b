[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ditqqq7uto5ar"
path.bptc="res://.godot/imported/Lit.png-14496f909609c7687bf38aa5f92e1a9b.bptc.ctex"
path.astc="res://.godot/imported/Lit.png-14496f909609c7687bf38aa5f92e1a9b.astc.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://Project/Models/Characters/KC1/KC1 Materials/Body/Lit.png"
dest_files=["res://.godot/imported/Lit.png-14496f909609c7687bf38aa5f92e1a9b.bptc.ctex", "res://.godot/imported/Lit.png-14496f909609c7687bf38aa5f92e1a9b.astc.ctex"]

[params]

compress/mode=2
compress/high_quality=true
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=1
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
