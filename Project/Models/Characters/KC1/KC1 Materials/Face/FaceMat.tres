[gd_resource type="ShaderMaterial" load_steps=7 format=3 uid="uid://13fgnsfqwtf1"]

[ext_resource type="Shader" uid="uid://ddp8r117pqtj4" path="res://addons/Godot-MToon-Shader/mtoon_cutout_cull_off.gdshader" id="1_8sdvu"]
[ext_resource type="Texture2D" uid="uid://cri41enyjnyjd" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Normal.png" id="2_8sdvu"]
[ext_resource type="Texture2D" uid="uid://cipkcixqyygyy" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Lit.png" id="4_pv7nl"]
[ext_resource type="Texture2D" uid="uid://c3oy2g85gkyd3" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Outline.png" id="5_pv7nl"]
[ext_resource type="Texture2D" uid="uid://dslyfvh56qejn" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Shade.png" id="6_6yaj1"]
[ext_resource type="Texture2D" uid="uid://do1ytc20dvjl7" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Mat.png" id="7_h23be"]

[resource]
resource_name = "N00_000_00_FaceMouth_00_FACE (Instance)"
render_priority = 0
shader = ExtResource("1_8sdvu")
shader_parameter/_AlphaCutoutEnable = 1.0
shader_parameter/_Cutoff = 0.5
shader_parameter/_Color = Color(0.943359, 0.878872, 0.878872, 1)
shader_parameter/_ShadeColor = Color(0.466667, 0.380392, 0.458824, 1)
shader_parameter/_MainTex = ExtResource("4_pv7nl")
shader_parameter/_MainTex_ST = Vector4(1, 1, 0, 0)
shader_parameter/_ShadeTexture = ExtResource("6_6yaj1")
shader_parameter/_BumpScale = 1.0
shader_parameter/_BumpMap = ExtResource("2_8sdvu")
shader_parameter/_ReceiveShadowRate = 1.0
shader_parameter/_ShadingGradeRate = 1.0
shader_parameter/_ShadeShift = -0.15
shader_parameter/_ShadeToony = 0.95
shader_parameter/_LightColorAttenuation = 1.0
shader_parameter/_IndirectLightIntensity = 0.1
shader_parameter/_RimColor = Color(0.521484, 0.521262, 0.464447, 1)
shader_parameter/_RimLightingMix = 1.0
shader_parameter/_RimFresnelPower = 50.0
shader_parameter/_RimLift = 0.33
shader_parameter/_MatcapColor = Color(1, 1, 1, 1)
shader_parameter/_SphereAdd = ExtResource("7_h23be")
shader_parameter/_EmissionColor = Color(0, 0, 0, 1)
shader_parameter/_EmissionMultiplier = 1.0
shader_parameter/_EmissionMap = ExtResource("7_h23be")
shader_parameter/_OutlineWidthMode = 0.0
shader_parameter/_OutlineWidthTexture = ExtResource("5_pv7nl")
shader_parameter/_OutlineWidth = 0.0
shader_parameter/_OutlineScaledMaxDistance = 1.0
shader_parameter/_OutlineColorMode = 0.0
shader_parameter/_OutlineColor = Color(0, 0, 0, 0)
shader_parameter/_OutlineLightingMix = 1.0
shader_parameter/_UvAnimScrollX = 0.0
shader_parameter/_UvAnimRotation = 0.0
shader_parameter/_UvAnimScrollY = 0.0
shader_parameter/_DebugMode = 0.0
shader_parameter/_MToonVersion = 34.0
