[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://vblqvnna6p41"
path="res://.godot/imported/KC01.vrm-eaa15032254dbcef89ccbbe354f20afc.scn"

[deps]

source_file="res://Project/Models/Characters/KC01.vrm"
dest_files=["res://.godot/imported/KC01.vrm-eaa15032254dbcef89ccbbe354f20afc.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
vrm/head_hiding_method=0
vrm/only_if_head_hiding_uses_layers/first_person_layers=2
vrm/only_if_head_hiding_uses_layers/third_person_layers=4
