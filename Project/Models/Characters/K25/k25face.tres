[gd_resource type="ShaderMaterial" load_steps=7 format=3 uid="uid://duus8u03rwygr"]

[ext_resource type="Shader" uid="uid://c7eohw8oxkv00" path="res://addons/Godot-MToon-Shader/mtoon_cutout_cull_off.gdshader" id="1_euci1"]
[ext_resource type="Texture2D" uid="uid://2bmrqmrwpiki" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Normal.png" id="2_ug6wr"]
[ext_resource type="Texture2D" uid="uid://ghd2cb1djj82" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Mat.png" id="3_sne6o"]
[ext_resource type="Texture2D" uid="uid://b5223va0axnp6" path="res://Project/Models/Characters/K25/textures/Face_Bake1_PBR_Ambient Occlusion.png" id="4_npqaf"]
[ext_resource type="Texture2D" uid="uid://bu011q6t0wl7x" path="res://Project/Models/Characters/KC1/KC1 Materials/Face/Outline.png" id="5_05i0j"]
[ext_resource type="Texture2D" uid="uid://ce40y8j3cqy3c" path="res://Project/Models/Characters/K25/textures/Image_0.png" id="6_e0dst"]

[resource]
resource_name = "N00_000_00_FaceMouth_00_FACE (Instance)"
render_priority = 0
shader = ExtResource("1_euci1")
shader_parameter/_AlphaCutoutEnable = 1.0
shader_parameter/_Cutoff = 0.5
shader_parameter/_Color = Color(0.196078, 0.0588235, 0.0588235, 1)
shader_parameter/_ShadeColor = Color(0.97, 0.81, 0.86, 1)
shader_parameter/_MainTex = ExtResource("4_npqaf")
shader_parameter/_MainTex_ST = Vector4(1, 1, 0, 0)
shader_parameter/_ShadeTexture = ExtResource("6_e0dst")
shader_parameter/_BumpScale = 1.0
shader_parameter/_BumpMap = ExtResource("2_ug6wr")
shader_parameter/_ReceiveShadowRate = 1.0
shader_parameter/_ShadingGradeRate = 1.0
shader_parameter/_ShadeShift = -0.15
shader_parameter/_ShadeToony = 0.95
shader_parameter/_LightColorAttenuation = 1.0
shader_parameter/_IndirectLightIntensity = 0.1
shader_parameter/_RimColor = Color(0.521484, 0.521262, 0.464447, 1)
shader_parameter/_RimLightingMix = 1.0
shader_parameter/_RimFresnelPower = 50.0
shader_parameter/_RimLift = 0.33
shader_parameter/_MatcapColor = Color(1, 1, 1, 1)
shader_parameter/_SphereAdd = ExtResource("3_sne6o")
shader_parameter/_EmissionColor = Color(0, 0, 0, 1)
shader_parameter/_EmissionMultiplier = 1.0
shader_parameter/_EmissionMap = ExtResource("3_sne6o")
shader_parameter/_OutlineWidthMode = 0.0
shader_parameter/_OutlineWidthTexture = ExtResource("5_05i0j")
shader_parameter/_OutlineWidth = 0.0
shader_parameter/_OutlineScaledMaxDistance = 1.0
shader_parameter/_OutlineColorMode = 0.0
shader_parameter/_OutlineColor = Color(0, 0, 0, 0)
shader_parameter/_OutlineLightingMix = 1.0
shader_parameter/_UvAnimScrollX = 0.0
shader_parameter/_UvAnimRotation = 0.0
shader_parameter/_UvAnimScrollY = 0.0
shader_parameter/_DebugMode = 0.0
shader_parameter/_MToonVersion = 34.0
