[gd_resource type="BehaviorTree" load_steps=21 format=3 uid="uid://cigv4blllbcjn"]

[ext_resource type="Script" uid="uid://cqglw8vdc56k1" path="res://Project/AI/tasks/random_position.gd" id="1_lm3dd"]
[ext_resource type="Script" uid="uid://co0syn0oedem1" path="res://Project/AI/tasks/move_to_position.gd" id="2_rf42x"]
[ext_resource type="Script" uid="uid://btt0iinjhhlv7" path="res://Project/AI/tasks/find_target.gd" id="3_vbgxw"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_gohpv"]
var/target/name = &"target"
var/target/type = 24
var/target/hint = 0
var/target/hint_string = ""
var/move_type/name = &"move_type"
var/move_type/type = 4
var/move_type/value = ""
var/move_type/hint = 0
var/move_type/hint_string = ""
var/pos/name = &"pos"
var/pos/type = 9
var/pos/value = Vector3(0, 0, 0)
var/pos/hint = 0
var/pos/hint_string = ""

[sub_resource type="BTConsolePrint" id="BTConsolePrint_lm3dd"]
text = "Enemy: Idle"

[sub_resource type="BTRandomWait" id="BTRandomWait_rf42x"]

[sub_resource type="BTAction" id="BTAction_rf42x"]
script = ExtResource("3_vbgxw")
group = &"Player"
target_var = &"target"
move_var = &"move_type"
in_range = 10.0

[sub_resource type="BTSequence" id="BTSequence_vbgxw"]
custom_name = "IdleSequence"
children = [SubResource("BTConsolePrint_lm3dd"), SubResource("BTRandomWait_rf42x"), SubResource("BTAction_rf42x")]

[sub_resource type="BBVariant" id="BBVariant_8ulsi"]
type = 4
saved_value = "player"
resource_name = "player"

[sub_resource type="BTCheckVar" id="BTCheckVar_w576u"]
variable = &"move_type"
value = SubResource("BBVariant_8ulsi")

[sub_resource type="BTConsolePrint" id="BTConsolePrint_rf42x"]
text = "Enemy: Move to Player"

[sub_resource type="BTAction" id="BTAction_8ulsi"]
script = ExtResource("2_rf42x")
target_position_var = &"pos"
direction_var = &"dir"
target_node_var = &"target"
move_var = &"move_type"
speed = 2
tolerance = 2
max_range = 10

[sub_resource type="BTSequence" id="BTSequence_pig6p"]
custom_name = "MoveToTargetSequence"
children = [SubResource("BTCheckVar_w576u"), SubResource("BTConsolePrint_rf42x"), SubResource("BTAction_8ulsi")]

[sub_resource type="BBVariant" id="BBVariant_rf42x"]
type = 4
saved_value = "random"
resource_name = "random"

[sub_resource type="BTCheckVar" id="BTCheckVar_vbgxw"]
variable = &"move_type"
value = SubResource("BBVariant_rf42x")

[sub_resource type="BTConsolePrint" id="BTConsolePrint_pig6p"]
text = "Enemy: Move to Random"

[sub_resource type="BTAction" id="BTAction_vbgxw"]
script = ExtResource("1_lm3dd")
position_var = &"pos"
direction_var = &"dir"

[sub_resource type="BTAction" id="BTAction_w576u"]
script = ExtResource("2_rf42x")
target_position_var = &"pos"
direction_var = &"dir"
target_node_var = &"target"
move_var = &"move_type"
speed = 2
tolerance = 2
max_range = 10

[sub_resource type="BTSequence" id="BTSequence_jkkh5"]
custom_name = "MoveToRandomPositionSequence"
children = [SubResource("BTCheckVar_vbgxw"), SubResource("BTConsolePrint_pig6p"), SubResource("BTAction_vbgxw"), SubResource("BTAction_w576u")]

[sub_resource type="BTSelector" id="BTSelector_5qdpp"]
children = [SubResource("BTSequence_vbgxw"), SubResource("BTSequence_pig6p"), SubResource("BTSequence_jkkh5")]

[resource]
blackboard_plan = SubResource("BlackboardPlan_gohpv")
root_task = SubResource("BTSelector_5qdpp")
