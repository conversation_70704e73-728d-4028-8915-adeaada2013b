extends BTAction

@export var position_var: StringName = &"pos"
@export var direction_var: StringName = &"dir"

func _tick(_delta: float) -> Status:
	var pos = random_pos()
	blackboard.set_var(position_var, pos)
	return SUCCESS

func random_pos():
	var vector: Vector3
	# Get from navigation region from map
	var map: RID = agent.nav_agent.get_navigation_map()
	var region: RID = NavigationServer3D.map_get_regions(map)[0]
	var pos: Vector3 = NavigationServer3D.region_get_random_point(region, 1, false)
	return pos
