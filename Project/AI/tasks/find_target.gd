extends BTAction

@export var group: StringName
@export var target_var: StringName = &"target"
@export var move_var: StringName = &"move_type"
@export var in_range: float = 40.0

var target

func _tick(_delta: float) -> Status:
	target = get_player_node()
	blackboard.set_var(target_var, target)
	if abs(agent.global_position.z - target.global_position.z) < in_range:
		blackboard.set_var(move_var, "player")
	else:
		blackboard.set_var(move_var, "random")
	return FAILURE

func get_player_node():
	var player = agent.get_tree().get_first_node_in_group(group)
	return player
