extends BTAction

@export var target_position_var := &"pos"
@export var direction_var := &"dir"
@export var target_node_var := &"target"
@export var move_var: StringName = &"move_type"

@export var speed = 2
@export var tolerance = 10
@export var max_range = 20

var target: Node = null
var target_pos: Vector3
var move_to_player: bool = false
var player = null

func _enter():
	player = agent.get_tree().get_first_node_in_group("player")
	var move_type = blackboard.get_var(move_var, "")
	# Player in range, so go to it
	if move_type == "player":
		move_to_player = true
		target = blackboard.get_var(target_node_var, null)
		target_pos = Vector3.ZERO
		print("move to target")
	else:
		move_to_player = false
		target = null
		target_pos = blackboard.get_var(target_position_var, Vector3.ZERO)
		agent.update_target_position(target_pos)
		print("move randomly")

func _tick(_delta: float) -> Status:
	if move_to_player:
		# Player still in range
		if agent.global_position.distance_to(target.global_position) < max_range:
			print("in range")
			agent.update_target_position(target.global_position)
		# Player out of range
		elif agent.global_position.distance_to(target.global_position) >= max_range:
			print("out of range")
			return FAILURE
	else:
		# Scan for player and push to next state
		if player_in_range():
			return SUCCESS
		print("random pos")
		# Walked to the the random position, so exit state
		if agent.global_position.distance_to(target_pos) < tolerance:
			move(0)
			return SUCCESS
	move(speed)
	return RUNNING

func player_in_range():
	if player:
		return agent.global_position.distance_to(player.global_position) < max_range
	return false

func move(_speed):
	var destination = agent.nav_agent.get_next_path_position()
	var local_destination = destination - agent.global_position
	var direction = local_destination.normalized()
	agent.velocity = direction * speed
	agent.look_at(destination)
