extends Node
class_name InventoryManager
# Dependencies and UI components
@export var slot_prefab: PackedScene
@export var inventory_parent: Node
@export var equip_parent: Node

@export var info_panel: Node
@export var local_inventory_system: InventorySystem

@export var head_slot: Node
@export var upper_body_slot: Node
@export var lower_body_slot: Node
@export var hands_slot: Node
@export var feet_slot: Node
@export var accessory_one_slot: Node
@export var accessory_two_slot: Node 

@export var player_resource: PlayerResource
var equip_parents: Array = []

@export var is_selecting: bool = false
var current_item: InventoryItem = null


func _ready():
	await get_tree().physics_frame
	equip_parents.append_array([head_slot, upper_body_slot, lower_body_slot, hands_slot, feet_slot, accessory_one_slot, accessory_two_slot])
	InventorySystem.inventory_update_event.connect(_on_update_inventory)
# Initialize equipment slots with default items (e.g., empty slot).
	_initialize_equipment_slots()

	_on_update_inventory()



	# Equip some initial items (for example, at the start of the game).
	_equip_initial_items()
	
	
# Initialize equipment slots with default items.
func _initialize_equipment_slots():
	# for slot_type in InventorySystem.SlotType.values():
	InventorySystem.player_inventory.player_inventory.equipped_items.clear()

# Equip some initial items (for example, a default weapon and armor).
func _equip_initial_items():
	if player_resource.head != null:
		var initial_head_item = InventoryItem.new(player_resource.head)
		InventorySystem.equip_item(initial_head_item, InventorySystem.SlotType.HEAD)

	if player_resource.upper_body != null:
		var initial_upper_body_item = InventoryItem.new(player_resource.upper_body)
		InventorySystem.equip_item(initial_upper_body_item, InventorySystem.SlotType.UPPER_BODY)

	if player_resource.lower_body != null:
		var initial_lower_body_item = InventoryItem.new(player_resource.lower_body)
		InventorySystem.equip_item(initial_lower_body_item, InventorySystem.SlotType.LOWER_BODY)

	if player_resource.hands != null:
		var initial_hands_item = InventoryItem.new(player_resource.hands)
		InventorySystem.equip_item(initial_hands_item, InventorySystem.SlotType.HANDS)

	if player_resource.feet != null:
		var initial_feet_item = InventoryItem.new(player_resource.feet)
		InventorySystem.equip_item(initial_feet_item, InventorySystem.SlotType.FEET)

func _on_update_inventory():
	for child in inventory_parent.get_children():
		if child != null:
			child.queue_free()

	for equip_par in equip_parents:
		for child in equip_par.get_children():
			if child != null:
				child.queue_free()

	_draw_inventory()
	player_resource.calculate_player_type()

func _draw_inventory():
	if local_inventory_system == null or InventorySystem.player_inventory == null:
		print("Inventory system or player inventory is null")
		return
	
	var item_slot_index = 0

	for item in InventorySystem.items.keys():
		_add_inventory_slot(item, item_slot_index)
		item_slot_index += 1

	# Create player equipment slots
	for slot_type in InventorySystem.SlotType :
		if InventorySystem.equipped.has(slot_type) :
			var item = InventorySystem.equipped[slot_type]
			_add_equip_slot(item, int(slot_type))
		else:
			_add_equip_slot(null, int(slot_type)) # Pass null for empty slots

	# Create empty inventory slots
	for i in range(item_slot_index, 12):
		_add_inventory_slot(null, item_slot_index) # Pass null for empty slots
		item_slot_index += 1
		

func _add_inventory_slot(item: InventoryItem, index: int):
	var obj = load(slot_prefab.resource_path).instantiate()
	obj.set("inventory_manager", self)
	obj.set("inventory_system", local_inventory_system)
	obj.set("inventory_so", InventorySystem.player_inventory)
	obj.set("index", index)
	obj.set("is_equip_slot", false)

	if item: 
		obj.set_with_item(item, index, false, self)
	else:
		obj.set_without_item(index, false, self) # Set as empty slot
		
	inventory_parent.add_child(obj)
	


func _add_equip_slot(item: InventoryItem, index: int):
	var obj = load(slot_prefab.resource_path).instantiate()
	var equip_parent = equip_parents[index]
	equip_parent.add_child(obj)
	
	obj.set("inventory_manager", self)
	obj.set("inventory_system", local_inventory_system)
	obj.set("inventory_so", InventorySystem.player_inventory)
	obj.set("index", index)
	obj.set("is_equip_slot", true)
	
	if item != null:
		obj.call("set_with_item", item, index, true, self)
		if item.data != null:
			item.data._current_slot = InventorySystem.SlotType.values()[index]
	else:
		obj.call("set_without_item", index, true, self) # Set as empty slot

func select_slot(item: InventoryItem):
	is_selecting = true
	current_item = item

func deselect_slot():
	is_selecting = false
	current_item = null
