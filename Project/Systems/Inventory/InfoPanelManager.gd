extends Control

@export var titleText : Label
@export var displayImage : Control

@export var descriptionText : Label
@export var typeText : Label
@export var classText : Label
@export var damageText : Label
@export var spellCostText : Label

func _ready():
	set_without_item()

func set_without_item():
	titleText.text = "Item"
	displayImage.texture = null
	descriptionText.text = "Description: /n"
	typeText.text = "Type: "
	classText.text = "Class: "
	damageText.text = "Damage: "
	spellCostText.text = "Cost: "

func set_with_item(item):
	titleText.text = item.data.display_name
	displayImage.texture = item.data.icon
	descriptionText.text = "Description: /n" + item.data.description
	typeText.text = "Type: " + str(item.data.special_ability_type)
	classText.text = "Class: " + str(item.data.spell_class)
	damageText.text = "Damage: " + str(item.data.spell_damage)
	spellCostText.text = "Cost: " + str(item.data.spell_cost)
