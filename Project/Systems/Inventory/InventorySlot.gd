extends Button

var inventory_system: InventorySystem
var inventory_manager: InventoryManager
@export var slot_index: int
@export var equip_slot: bool = false
@export var equip_slot_index: int

var is_populated = false
var item = null



func set_with_item(item: InventoryItem, index: int, equip: bool, manager: InventoryManager):
	equip_slot = equip
	if equip:
		equip_slot_index = index
	else:
		slot_index = index
	is_populated = true
	self.item = item
	if item.data.icon:
		expand_icon = true;
		icon = item.data.icon
		get_parent().self_modulate = Color.TRANSPARENT
		#icon.modulate = Color.WHITE
	text = item.data.display_name
	#stack_obj.visible = false
	inventory_manager = manager
	print(slot_index)


func set_without_item(index: int, equip: bool, manager: InventoryManager):
	equip_slot = equip
	if equip:
		equip_slot_index = index
	else:
		slot_index = index

	is_populated = false
	#stack_obj.visible = false
	inventory_manager = manager
	print(slot_index)



func _on_focus_entered() -> void:
	if item != null:
		inventory_manager.info_panel.set_with_item(item)

func _on_focus_exited() -> void:
	inventory_manager.info_panel.set_without_item()

#func _on_pressed() -> void:
	#if is_populated:
		#if !equip_slot:
			#if !inventory_manager.is_selecting:
				#inventory_manager.select_slot(item)
			#elif inventory_manager.is_selecting:
				#inventory_manager.deselect_slot()
#
		#elif equip_slot:
			##if !inventory_manager.is_selecting:
				##inventory_manager.select_slot(item)
			##elif inventory_manager.is_selecting:
			#if is_populated:
				#inventory_system.unequip_item(equip_slot_index) #unequip
				#inventory_manager.deselect_slot()
			#elif !is_populated and inventory_manager.is_selecting:
				#inventory_system.equip_item(inventory_manager.current_item, equip_slot_index)
				#inventory_manager.deselect_slot()


func _on_button_down() -> void:
	print("pressed")
	if is_populated:
		if !equip_slot and !inventory_manager.is_selecting:
			if item == null: return
			inventory_manager.select_slot(item)
		elif equip_slot and !inventory_manager.is_selecting:
			inventory_system.unequip_item(equip_slot_index) #unequip
		elif equip_slot and inventory_manager.is_selecting:
			inventory_system.equip_item(inventory_manager.current_item, equip_slot_index)
			inventory_manager.deselect_slot()
	elif equip_slot and inventory_manager.is_selecting:
		inventory_system.equip_item(inventory_manager.current_item, equip_slot_index)
		inventory_manager.deselect_slot()
