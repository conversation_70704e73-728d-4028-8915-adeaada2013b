extends Node

# Exported variables
@export var target_avatar : Node

# Dictionary to map bone names to their Transform nodes
var target_avatar_bone_map : Dictionary = {}



func _ready():
	target_avatar = self
	init()

func init():
	assert(target_avatar != null)

	# Create bone maps for the target avatar
	for bone in target_avatar.get_tree().get_nodes_in_group("bones"):
		if not target_avatar_bone_map.has(bone.name):
			target_avatar_bone_map[bone.name] = bone
		else:
			print("Duplicate bone name: %s" % bone.name)

func fix_magica(clothing : Node) -> EquipInfo:
	var equip_info = EquipInfo.new()
	equip(clothing, equip_info)
	equip_info.equip_object.set("layer", 9)
	
	# Change layer for children
	_set_layer_for_children(equip_info.equip_object, 9)
	
	return equip_info

func _set_layer_for_children(node : Node, layer : int):
	for child in node.get_children():
		child.set("layer", layer)
		_set_layer_for_children(child, layer)

func equip(equip_prefab : Node, einfo : EquipInfo) -> EquipInfo:
	assert(equip_prefab != null)

	# Instantiate the equipment prefab
	var gobj = equip_prefab.instance()
	target_avatar.add_child(gobj)
	
	# Collect all colliders from the prefab
	var collider_list = []
	for collider in gobj.get_tree().get_nodes_in_group("colliders"):
		collider_list.append(collider)
	
	# Replace bones in SkinnedMeshRenderer (if applicable)
	# This is just a placeholder; implement if necessary

	# Move all colliders to the new avatar
	move_colliders(collider_list)

	# Record information for release
	einfo.equip_object = gobj
	einfo.collider_list = collider_list
	return einfo

func move_colliders(collider_list : Array):
	for collider in collider_list:
		var parent = collider.get_parent()
		if parent and target_avatar_bone_map.has(parent.name):
			var new_parent = target_avatar_bone_map[parent.name]

			# Set the parent and retain local position and rotation
			var local_position = collider.position
			var local_rotation = collider.rotation_degrees
			collider.remove_child()
			new_parent.add_child(collider)
			collider.position = local_position
			collider.rotation_degrees = local_rotation

func remove(einfo : EquipInfo):
	if einfo.equip_object != null:
		einfo.equip_object.queue_free()
	
	for collider in einfo.collider_list:
		collider.queue_free()
	
	einfo.equip_object = null
	einfo.collider_list.clear()
