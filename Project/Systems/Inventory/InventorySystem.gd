extends Node

signal inventory_update_event
# Variables
var player_inventory: InventoryClass
var items
var equipped

func _init() -> void:
	player_inventory = InventoryClass.new()
	items = player_inventory.inventory.items
	equipped = player_inventory.player_inventory.equipped_items

func _ready():
	process_mode = PROCESS_MODE_WHEN_PAUSED

func initialise():
	player_inventory.initialise();
	for slot_type in player_inventory.player_inventory.equipped_items.keys():
		var item = player_inventory.player_inventory.equipped_items[slot_type]
		attach(item.data.slot_prefabs[int(slot_type)])

func add_item(item: InventoryItem, quantity: int = 1):
	if item != null:
		if player_inventory.inventory.items.has(item):
			player_inventory.inventory.items[item] += quantity
		else:
			player_inventory.inventory.items[item] = quantity
	else:
		print("Cannot add null item to the inventory.")
	emit_signal("inventory_update_event")

func remove_item(item: InventoryItem, quantity: int = 1):
	if item != null:
		if player_inventory.inventory.items.has(item):
			player_inventory.inventory.items[item] -= quantity
			if player_inventory.inventory.items[item] <= 0:
				player_inventory.inventory.items.erase(item)
		else:
			print("Item not found in the inventory.")
	else:
		print("Cannot remove null item from the inventory.")
	emit_signal("inventory_update_event")

func equip_item(item: InventoryItem, slot_type: SlotType):
	if player_inventory.player_inventory.equipped_items.has(slot_type):
		if player_inventory.player_inventory.equipped_items[slot_type] != null:
			unequip_item(slot_type)

		#player_inventory.player_inventory.equipped_items[slot_type] = item
		player_inventory.player_inventory.equipped_items[slot_type].equip_info = attach(item.data.slot_prefabs[int(slot_type)])
		if item != null:
			print("null") #
	else:
		print("Invalid equipment slot type.")
	remove_item(item)
	emit_signal("inventory_update_event")

func unequip_item(slot_type: SlotType):
	if player_inventory.player_inventory.equipped_items.has(slot_type):
		detach(player_inventory.player_inventory.equipped_items[slot_type].equip_info)
		add_item(player_inventory.player_inventory.equipped_items[slot_type])
		player_inventory.player_inventory.equipped_items[slot_type] = null
	else:
		print("Invalid equipment slot type.")
	emit_signal("inventory_update_event")

func attach(item: Node) -> EquipInfo:
	#if item != null:
		#return fix_magica.fix_magica(item)
	#return null
	return

func detach(info: EquipInfo):
	#if info != null:
		#fix_magica.remove(info)
		return

# SlotType enum equivalent in GDScript
enum SlotType {
	HEAD,
	UPPER_BODY,
	LOWER_BODY,
	HANDS,
	FEET,
	ACCESSORY_ONE,
	ACCESSORY_TWO
}
