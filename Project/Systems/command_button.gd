extends Button


var combat
var pos: Vector2
var spell: Spell
var int_value: int



func _ready():
	combat = get_tree().get_nodes_in_group("Systems").front()
	pos = position


func set_data(spell_d : Spell, type: InventorySystem.SlotType):
	text = spell_d.display_name 

func _on_confirmed():
	#AudioManager.play_one_shot(FMODEvents.submitButton, position)
	combat.set_aim_camera(true)
	combat.ability(spell)

func _on_selected():
	#AudioManager.play_one_shot(FMODEvents.selectButton, position)
	#$AnimationPlayer.play("select")
	pass

func command(i: int, sp: Spell):
	spell = sp
	int_value = i


func set_aim_camera():
	combat.set_aim_camera(true)


func _on_focus_entered() -> void:
	await get_tree().process_frame
	_on_selected()

func _on_button_up() -> void:
	await get_tree().process_frame
	_on_confirmed()
