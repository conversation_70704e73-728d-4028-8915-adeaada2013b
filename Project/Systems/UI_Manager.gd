extends Control

signal attack_action
@export var local_combat_system: GlobalCombatSystem
@export var commandPrefab: PackedScene

@onready var wholeCanvas = $"."
@onready var tacticalCanvas = $LucidMode
@onready var attackCanvas = $LucidIndicator
@onready var commandsGroup = $LucidMode/LucidModeBlock/TargetGroup/CommandGroup/VBoxContainer
@onready var targetGroup = $LucidMode/LucidModeBlock/TargetGroup/VBoxContainer
@onready var aimCanvas = $AimTarget
@onready var healthSlider: ProgressBar = $CharStats/MarginContainer/VBoxContainer/Health
@onready var manaSlider: ProgressBar = $CharStats/MarginContainer/VBoxContainer/Aura
@onready var atbCompleteLeft = $CharStats/MarginContainer/VBoxContainer/Aura/HBoxContainer/AuraLeft
@onready var atbCompleteRight = $CharStats/MarginContainer/VBoxContainer/Aura/HBoxContainer/AuraRight
@onready var battleIndicator = $Control/BattleIndicator
@onready var char_name = $CharStats/MarginContainer/VBoxContainer/Name
@onready var character_resource = local_combat_system.character_resource

func _ready():
	local_combat_system.connect("on_attack",  _on_attack_action)
	local_combat_system.connect("on_set_mana", _on_mana_changed)
	local_combat_system.connect("on_set_health", _on_health_changed)
	local_combat_system.connect("on_tactical_trigger", _on_tactical_trigger)
	local_combat_system.connect("on_target_select_mode", _on_target_select_trigger)
	local_combat_system.connect("on_battle_mode", _on_battle_mode)
	char_name.text = local_combat_system.character_resource.player_name
	healthSlider.max_value = character_resource.max_health
	manaSlider.max_value = character_resource.max_mana
	_on_health_changed()
	_on_mana_changed()
	_on_battle_mode(false)

func _on_attack_action():
	attack_action.emit()

func _on_mana_changed():
	manaSlider.value = character_resource.mana_count
	atbCompleteLeft.self_modulate.a = 1.0 if character_resource.mana_count >= 100 else 0.0
	atbCompleteRight.self_modulate.a = 1.0 if character_resource.mana_count >= 200 else 0.0

func _on_health_changed():
	healthSlider.value = character_resource.health_count

func _on_tactical_trigger(on):
	_on_battle_mode(on)
	tacticalCanvas.visible = on
	commandsGroup.visible = on
	targetGroup.visible = !on
	if on:
		for i in range(commandsGroup.get_child_count()):
			var slot = commandsGroup.get_child(i)
			slot.queue_free()
		for slotType in InventorySystem.SlotType.keys():
			if InventorySystem.equipped.has(slotType):# and InventorySystem.equipped[slotType] != null and InventorySystem.equipped[slotType].data != null:
				add_command(character_resource.equipped[slotType].data, slotType)
#		commandsGroup.get_child(0).grab_focus()


func add_command(spell, slotType):
	var obj = commandPrefab.instance()
	commandsGroup.add_child(obj)
	obj.command_button.set_data(spell, slotType)

func _on_target_select_trigger(on):
	targetGroup.visible = on
	if on:
		for child in targetGroup.get_child_count():
			if local_combat_system.targets.size() >= 1:
				targetGroup.get_child(child).visible = true
				targetGroup.get_child(child).text = local_combat_system.targets[child]
			else:
				targetGroup.get_child(child).visible = false
	aimCanvas.visible = on
	targetGroup.get_child(0).grab_focus()

func _on_battle_mode(on):
	_on_health_changed()
	_on_mana_changed()
	battleIndicator.visible = on
	wholeCanvas.modulate.a = 1 if on else 0
