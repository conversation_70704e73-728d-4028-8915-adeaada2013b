[gd_resource type="Resource" script_class="Spell" load_steps=2 format=3 uid="uid://c6k2a4p1q0k0g"]

[ext_resource type="Script" uid="uid://dvvydl5xvn6cj" path="res://Project/Systems/Resources/Base/SpellResource.gd" id="1_b2a5n"]

[resource]
script = ExtResource("1_b2a5n")
display_name = "fungus"
id = 0
description = "this spell is fungus"
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false
