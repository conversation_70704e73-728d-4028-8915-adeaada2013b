
extends Resource

class_name Character

@export var character_prefab: PackedScene
@export var inventory_resource: Resource

# Profile
@export var actor: DialogicCharacter  # Assuming `DialogueActor` will be a node in Godot
@export var player_name: String = "name"

# Inventory
@export var head: Spell
@export var upper_body: Spell
@export var lower_body: Spell
@export var hands: Spell
@export var feet: Spell

# Stats
@export var attack: int
@export var defense: int
@export var speed: int
@export var max_health: int = 200
@export var health_count: int = 200:
	set(value):
		health_count = set_health(value)

@export var max_mana: int = 100
@export var mana_count: int = 100:
	set(value):
		mana_count = set_mana(value)


# Enum for character class
enum CharSOClass {
	ELEMENTAL,
	VITAL,
	SPECTRAL,
	ETHEREAL,
	MATERIAL,
	AUGMENTAL,
	NONE
}

@export var char_so_class: CharSOClass = CharSOClass.NONE

# Level
@export var level: int
@export var experience: float
@export var player_location: Vector3



func set_health(value):
	return value

func set_mana(value):
	return value
