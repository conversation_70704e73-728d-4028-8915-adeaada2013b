extends Resource

class_name Inventory

@export var head: Spell
@export var upper_body: Spell
@export var lower_body: Spell
@export var hands: Spell
@export var feet: Spell
@export var player: Character
@export var inventory: Inventory
#@export var player_inventory: PlayerInventory

func initialise():
	# Initialize your inventory here if needed
	pass

func equip(item: InventoryItem) -> InventoryItem:
	var spell = item.data
	print("equip")
	
	# Example of how to equip based on some condition (adapted to Godot)
	# if spell.spell_type == SpellSO.spelltype.area:
	#     if head != null:
	#         unequip(head)
	#     head = spell

	return null

func unequip(item: InventoryItem) -> void:
	print("unequip")
	var spell = item.data
	
	# Example of how to unequip based on some condition (adapted to Godot)
	# if spell.spell_type == SpellSO.spelltype.area:
	#     head = null
