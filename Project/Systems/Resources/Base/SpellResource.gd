extends Resource

class_name Spell

@export var display_name: String
@export var id: int
@export var description: String
@export var icon: Texture2D

@export var special_ability_type: SpecialAbilityType
@export var spell_class: SpellClass

@export var _current_slot: InventorySystem.SlotType # Assuming InventorySystem.SlotType will be an integer in Godot
@export var spell_damage: int
@export var spell_defense: int
@export var spell_cost: int
@export var spell_range: float

@export var spell_resource: PackedScene

@export var head_resource: PackedScene
@export var upper_body_resource: PackedScene
@export var lower_body_resource: PackedScene
@export var hand_resource: PackedScene
@export var feet_resource: PackedScene

@export var anim_trigger: String
@export var VFX: Array[PackedScene] = []

@export var takes_two_slots: bool = false
@export var is_accessory: bool = false

var slot_resources: Array[PackedScene] = []

#func _init():
	#slot_resources.append(head_resource)
	#slot_resources.append(upper_body_resource)
	#slot_resources.append(lower_body_resource)
	#slot_resources.append(hand_resource)
	#slot_resources.append(feet_resource)

# Enum for special abilities
enum SpecialAbilityType {
	AREA,
	RANGED,
	SELF,
	SUMMON
}

# Enum for spell classes
enum SpellClass {
	ELEMENTAL,
	VITAL,
	SPECTRAL,
	ETHEREAL
}
