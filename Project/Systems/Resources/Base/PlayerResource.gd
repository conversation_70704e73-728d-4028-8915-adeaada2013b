extends Character

class_name PlayerResource

func _init():
	update_character_info()
	update_inventory()
	calculate_player_type()
	
func set_health(value):
	return value

func set_mana(value):
	return value

func update_character_info():
	if character_prefab != null:
			# Assuming you have a method to get the actor node
			actor = character_prefab.get_node("ActorNode")
			player_name = actor.get("actor_name")


func update_inventory():
	if inventory_resource != null:
		if inventory_resource.head: head = inventory_resource.head
		if inventory_resource.upper_body: upper_body = inventory_resource.upper_body
		if inventory_resource.lower_body: lower_body = inventory_resource.lower_body
		if inventory_resource.hands: hands = inventory_resource.hands
		if inventory_resource.feet: feet = inventory_resource.feet
		#inventory.player = self

func calculate_player_type():
	var e = 0
	var v = 0
	var s = 0
	var eth = 0

	if inventory_resource != null:
		for item in inventory_resource.player_inventory.equipped_items.values():
			if item and item.data:
				match item.data.spell_class:
					Spell.SpellClass.ELEMENTAL:
						e += 1
					Spell.SpellClass.VITAL:
						v += 1
					Spell.SpellClass.SPECTRAL:
						s += 1
					Spell.SpellClass.ETHEREAL:
						eth += 1

		var spell_types = {
			Spell.SpellClass.ELEMENTAL: e,
			Spell.SpellClass.VITAL: v,
			Spell.SpellClass.SPECTRAL: s,
			Spell.SpellClass.ETHEREAL: eth
		}

		var max_count = 0
		var maxim = {"key": null, "value": 0}

		for key in spell_types.keys():
			if spell_types[key] > maxim["value"]:
				maxim = {"key": key, "value": spell_types[key]}
				max_count = 1
			elif spell_types[key] == maxim["value"]:
				max_count += 1

		var type_key = maxim["key"]

		if type_key != null:
			match type_key:
				Spell.SpellClass.ELEMENTAL:
					char_so_class = CharSOClass.ELEMENTAL
				Spell.SpellClass.VITAL:
					char_so_class = CharSOClass.VITAL
				Spell.SpellClass.SPECTRAL:
					char_so_class = CharSOClass.SPECTRAL
				Spell.SpellClass.ETHEREAL:
					char_so_class = CharSOClass.ETHEREAL

		if max_count > maxim["value"]:
			char_so_class = CharSOClass.NONE
