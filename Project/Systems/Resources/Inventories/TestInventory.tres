[gd_resource type="Resource" script_class="Inventory" load_steps=7 format=3 uid="uid://bc1in3k7whclb"]

[ext_resource type="Script" uid="uid://dsyvrplm1fext" path="res://Project/Systems/Resources/Base/InventoryResource.gd" id="1_qxbsn"]
[ext_resource type="Script" uid="uid://dvvydl5xvn6cj" path="res://Project/Systems/Resources/Base/SpellResource.gd" id="2_ni50s"]
[ext_resource type="PackedScene" uid="uid://dt6gim6unvpo4" path="res://Project 2024/Models/Outfit/fit_UpperBody.fbx" id="3_t6uq1"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_fap54"]

[sub_resource type="NoiseTexture3D" id="NoiseTexture3D_d8awn"]
noise = SubResource("FastNoiseLite_fap54")

[sub_resource type="Resource" id="Resource_bqntx"]
script = ExtResource("2_ni50s")
display_name = "Bongus"
id = 0
description = "Bongus"
icon = SubResource("NoiseTexture3D_d8awn")
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
upper_body_resource = ExtResource("3_t6uq1")
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[resource]
script = ExtResource("1_qxbsn")
upper_body = SubResource("Resource_bqntx")
