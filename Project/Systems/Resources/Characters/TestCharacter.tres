[gd_resource type="Resource" script_class="Character" load_steps=8 format=3 uid="uid://ctm8cq5mlqymq"]

[ext_resource type="Script" uid="uid://dvvydl5xvn6cj" path="res://Project/Systems/Resources/Base/SpellResource.gd" id="1_0tnmh"]
[ext_resource type="Script" uid="uid://dunn0sa07e1eh" path="res://Project/Systems/Resources/Base/CharacterResource.gd" id="1_vpfe6"]

[sub_resource type="Resource" id="Resource_v4yrg"]
script = ExtResource("1_0tnmh")
display_name = ""
id = 0
description = ""
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[sub_resource type="Resource" id="Resource_v0am4"]
script = ExtResource("1_0tnmh")
display_name = ""
id = 0
description = ""
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[sub_resource type="Resource" id="Resource_m3ums"]
script = ExtResource("1_0tnmh")
display_name = ""
id = 0
description = ""
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[sub_resource type="Resource" id="Resource_7yu68"]
script = ExtResource("1_0tnmh")
display_name = ""
id = 0
description = ""
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[sub_resource type="Resource" id="Resource_3yjgc"]
script = ExtResource("1_0tnmh")
display_name = ""
id = 0
description = ""
special_ability_type = 0
spell_class = 0
_current_slot = 0
spell_damage = 0
spell_defense = 0
spell_cost = 0
spell_range = 0.0
anim_trigger = ""
VFX = Array[PackedScene]([])
takes_two_slots = false
is_accessory = false

[resource]
script = ExtResource("1_vpfe6")
player_name = "Bongus"
head = SubResource("Resource_m3ums")
upper_body = SubResource("Resource_3yjgc")
lower_body = SubResource("Resource_7yu68")
hands = SubResource("Resource_v0am4")
feet = SubResource("Resource_v4yrg")
attack = 0
defense = 0
speed = 0
max_health = 200
health_count = 200
max_mana = 100
mana_count = 100
char_so_class = 1
level = 0
experience = 0.0
player_location = Vector3(0, 0, 0)
