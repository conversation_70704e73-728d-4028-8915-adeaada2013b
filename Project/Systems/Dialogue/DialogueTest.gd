@tool
extends Node
var outline : ShaderMaterial = preload("res://Project 2024/Models/Characters/MainCharacter/Main Character Materials/outline.tres")
@onready var default_material : Material = self.mesh.surface_get_material(0).duplicate()
var selector : Control
var star_ui : PackedScene = preload("res://Project/Prefabs/star_ui.tscn")
var offset : Vector3 = Vector3(0,1,0)
var star
@export var spell: Spell
@export var timeline: String

var timeline_value: String:
	get:
		return timeline_value
	set(value):
		timeline_value = value
		timeline = "res://Project/Dialogic Files/Timelines/" + value.substr(2).trim_suffix("\"]")
		notify_property_list_changed.call_deferred()

var spell_path:
	get:
		return spell_path
	set(value):
		spell_path = value
		spell = load("res://Project/Systems/Resources/Spells/" + value.substr(2).trim_suffix("\"]"))
		notify_property_list_changed.call_deferred()

		
@export_enum("Club", "MainMenu", "Shop") var level_select: String
@export_enum("Conversation:0", "Item:1", "Door:2") var mode: int


#@export_enum("Warrior", "Magician", "Thief") var character_class: int
#@export_enum("Slow:30", "Average:60", "Very Fast:200") var character_speed: int


func _get_property_list():
	var properties =[]
	
	properties.append({
		"name": "timeline_value",
		"type": TYPE_STRING,
		"hint": PROPERTY_HINT_ENUM_SUGGESTION,
		"hint_string": DirAccess.get_files_at("res://Project/Dialogic Files/Timelines/"),
	})

	properties.append({
		"name": "spell_path",
		"type": TYPE_STRING,
		"hint": PROPERTY_HINT_ENUM_SUGGESTION,
		"hint_string": DirAccess.get_files_at("res://Project/Systems/Resources/Spells/"),
	})
	
	return properties

func _ready() -> void:
	selector = get_tree().get_root().find_child("Selector",true,false) 
	selector.set_visibility(false)
	
func _on_area_3d_focused(interactor: Interactor) -> void:
	print("FOCUSED")
	star = star_ui.instantiate()
	add_child(star) 
	star.position = star.position + offset
	selector.set_visibility(true)
	if spell:
		selector.set_text(spell.display_name, spell.description)
	else:
		selector.set_text("test", "test description")
	self.material_override = default_material
	self.material_override.next_pass = outline

func _on_area_3d_interacted(interactor: Interactor) -> void:
	selector.visible = false
	if star and is_instance_valid(star): star.queue_free()
	
	if mode == 0:
		if Dialogic.current_timeline != null:
			return
		Dialogic.start(timeline).process_mode = Node.PROCESS_MODE_ALWAYS
		# get_viewport().set_input_as_handled()
	elif mode == 1:
		if spell:
		#TODO PLAY A SOUND
			InventorySystem.add_item(InventoryItem.new(spell), 1)
			process_mode = PROCESS_MODE_DISABLED
	elif mode == 2:
		SceneManager.change_scene_to_node(level_select)


	
func _on_area_3d_unfocused(interactor: Interactor) -> void:
	selector.set_visibility(false)
	if star and is_instance_valid(star): star.queue_free()
	print("UNFOCUSED")
	self.material_override = null
