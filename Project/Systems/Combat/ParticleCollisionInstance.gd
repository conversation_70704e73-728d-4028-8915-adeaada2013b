extends Area3D

class_name ParticleCollisionInstance

var effects_on_collision = []
var destroy_time_delay = 5
var use_world_space_position = false
var offset = 0
var rotation_offset = Vector3(0, 0, 0)
var use_only_rotation_offset = true
var use_fire_point_rotation = false
var destroy_main_effect = true
var damage = 10

func _on_ParticleSystem_particle_collision(other):
	var num_collision_events = self.get_overlapping_bodies()
	
	
	for i in num_collision_events:
		for effect in effects_on_collision:
			var instance = effect.new
			instance.transform.origin = i.transform + i.normal * offset
			if not use_world_space_position:
				instance.transform.parent = self
			if use_fire_point_rotation:
				instance.transform.look_at(self.transform.origin, Vector3(0, 1, 0))
			elif rotation_offset != Vector3.ZERO and use_only_rotation_offset:
				instance.transform.basis = Basis(rotation_offset)
			else:
				instance.transform.basis = Basis(instance.transform.basis * rotation_offset)
			add_child(instance)
			instance.connect("tree_exited", self, "_on_effect_tree_exited", [instance])
		
		if other.is_in_group("Enemies"):
			GlobalValues.character_resource.mana_count-=25

			if other.get_node("CharacterManager") != null:
				var cm = other.get_node("CharacterManager")
				cm.get_hit(self.transform)
				cm.damage(damage)

			# Damages an AI to the collided object
			if other.get_node("IDamageable") != null:
				other.get_node("IDamageable").damage(damage, self.transform, 100)
			# Damages an AI's location based damage component
			elif other.get_node("LocationBasedDamageArea") != null:
				var LBDArea = other.get_node("LocationBasedDamageArea")
				LBDArea.damage_area(damage, self.transform, 100)

func _on_effect_tree_exited():
	if destroy_main_effect == true:
		queue_free()
