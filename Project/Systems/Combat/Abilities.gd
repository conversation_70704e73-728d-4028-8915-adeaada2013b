extends Node3D

class_name Abilities

@export var weapon_damage: int = 5
@export var local_combat_system: GlobalCombatSystem 
var cs: GlobalCombatSystem
var player: Node3D
var movedir: Vector3
var moving: bool
var targets: Array

###
# Basic physical combat stuff
###
func set_combat_system(system: GlobalCombatSystem):
	cs = system
	
func _ready() -> void:
	get_player()
	#await get_tree().physics_frame
	#cs = get_parent().get_script() as GlobalCombatSystem
	#cs.weapon.damage = weapon_damage

func get_player():
	if cs:
		player = cs.player as Player

#func swing():
	#set_battle_mode(true)
	#set_animation_trans("swing")
	#move_towards_target()
	#attack_emit()
#
#func kick():
	#set_battle_mode(true)
	#set_animation_trans("kick") 
	#set_mana(-25)
	#attack_emit()
#
#func dodge():
	#get_moving()
	#get_movedir()
	#if moving:
		#set_animation_trans("dodge")
		#player.move_to(Vector3(player.position.x + 3 * movedir.x, player.position.y, player.position.z + 2 * movedir.z), .5)
#
#func block():
	#get_moving()
	#if !moving and cs._isBlocking:
		#cs._isBlocking = true
		#set_animation_trans("block")
		#cs.shield_object.visible = true
	#else:
		#cs._isBlocking = false
		#cs.shield_object.visible = false


func parry():
		set_animation_trans("parry")

func melee(spell):
	move_towards_target()
	set_animation_trans(spell.anim_trigger)
	damage_enemy(spell)
	if spell.VFX:
		for vfx in spell.VFX:
			var r = vfx.new(cs.transform)
			set_projectile_damage(r,spell)
	attack_emit()

###
# The more complex spell based abilities
###

func area_attack(spell):
	set_animation_trans(spell)
	damage_enemy(spell)
	trigger_vfx(spell, player.transform)
	attack_emit()

func self_attack(spell):
	set_animation_trans(spell)
	damage_enemy(spell)
	trigger_vfx(spell, player.transform)
	attack_emit()

func summon(spell):
	set_animation_trans(spell)
	damage_enemy(spell)
	trigger_vfx(spell, player.transform)
	attack_emit()

func ranged(spell):
	await look_at_target()
	set_animation_trans(spell)
	trigger_vfx(spell, cs.shoot_point)
	attack_emit()

###
# The functions used to grab combat system stuff for all of the abilities
###

func get_movedir():
	movedir = cs.player.get_movement_direction()

func get_moving():
	moving = cs.player.is_movement_ongoing()

func set_battle_mode(value):
	cs.set_battle_mode(value)

func get_targets():
	targets = cs.targets

func set_animation_trans(trans):
	if trans is String:
		cs.anim.set("parameters/Transition/transition_request", trans)
	elif trans is Spell:
		if trans.anim_trigger != null:
			trans = trans.anim_trigger
			cs.aniim.set("parameters/Transition/transition_request", trans)
	else: pass

func move_towards_target():
	get_targets()
	if targets != null and targets.size() > 0:
		await look_at_target()
		cs.MoveTowardsTargget(cs.targets[cs.targetIndex])

func look_at_target():
	cs.look_at(cs.targets[cs.targetnIndex].position, Vector3(0, 1, 0), true)

func set_mana(value):
	cs.set_mana(value)

func attack_emit():
	cs.on_attack.emit()

func damage_enemy(spell):
	cs.targets[cs.targetIndex].get_component(Character).health_count-=spell.spell_damage

func set_projectile_damage(r, spell):
	r.get_component(ParticleCollisionInstance).damage = calculate_damage(spell)

func trigger_vfx(spell, trans):
	if spell.VFX[0]:
		for vfx in spell.VFX:
			var r = vfx.new(trans)
			set_projectile_damage(r,spell)
	
##
# Stuff for calculating spell damage
# I want to expand further on this maybe look into yugioh, FF and pokemon examples
##

func calculate_damage(spell):
	get_targets()
	var damage = spell.spell_damage
	var target = targets[cs.targetIndex].get_component(Character)._class
	var d = ((((((2 * cs.character.level) + 2) / 5) * damage) / 50) + 2) * TypeEffectChart.get_effectiveness(spell, target,cs)
	return int(d)

class TypeEffectChart:
	static func get_effectiveness(spell,target,cs):
		
		var row = spell
		var col = target

		if cs.character_resource.CharSOClass == row:
			return chart[row][col] + .25
		else:
			return chart[row][col]

	static var chart = [
		[1.0, 1.0, 1.0, 1.0],
		[1.0, 1.0, 1.0, 1.0],
		[1.0, 1.0, 1.0, 1.0],
		[1.0, 1.0, 1.0, 1.0],
	]
