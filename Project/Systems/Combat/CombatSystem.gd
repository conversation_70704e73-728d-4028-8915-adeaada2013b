extends Node3D

class_name GlobalCombatSystem

signal on_attack
signal on_set_mana
signal on_set_health
signal on_tactical_trigger(on)
signal on_target_select_mode(on)
signal on_battle_mode(on)

@export var player: CharacterBody3D
@export var anim: AnimationTree
@export var character_resource: Character
@export var aim_object: Node3D
@export var shoot_point: Node
@export var aim_camera: Camera3D
@export var inspect_camera: Camera3D
@export var shield_object = Node3D
@export var weapon = Node3D
@export var local_inventory_system: InventorySystem

static var _battleMode = false
static var _tacticalMode = false
static var _isBlocking = false
static var _usingAbility = false
static var _dashing = false
static var _isAiming = false

var slowMotionTime = 0.005
var targetIndex = 0
var og_aim: Vector3 = Vector3(0, 0, 0)

var player_physics: PhysicsBody3D
var targets = []
var abilities: Abilities

func _ready():
	abilities = preload("res://Project/Systems/Combat/Abilities.gd").new()
	add_child(abilities)
	abilities.set_combat_system(self)
	
var maxMana = 100:
	set(value):
		set_max_mana(value)
		character_resource.max_mana = value
var mana = 100:
	set(value):
		set_mana(value)
		character_resource.mana_count = value
		on_set_mana.emit()
var maxHealth = 100:
	set(value):
		set_max_health(value)
		character_resource.max_health = value
var health = 100:
	set(value):
		set_health(value)
		character_resource.health_count = value
		on_set_health.emit()


func _process(_delta):
	if _battleMode && targets.size() > 0:
		targetIndex = nearest_target_to_center()
		aim_object.look_at(targets[targetIndex], Vector3(0, 1, 0))


func _input(event: InputEvent) -> void:
	_isBlocking = event.is_action_pressed("block")
	if _isBlocking:
		set_tactical_mode(true)

func _on_Area3D_body_entered(body):
	if body.is_in_group("Enemies"):
		targets.append(body)


func _on_Area3D_body_exited(body):
	if body.is_in_group("Enemies"):
		targets.erase(body)
		if targets.size() == 0:
			set_battle_mode(false)


func set_max_mana(value):
	maxMana = value
	on_set_mana.emit()

func set_mana(value):
	mana = clamp(value, 0, maxMana)
	on_set_mana.emit()

func set_max_health(value):
	maxHealth = value
	on_set_health.emit()

func set_health(value):
	health = clamp(value, 0, maxHealth)
	on_set_health.emit()

func ability(value: Spell):
	if value:
		set_tactical_mode(false)
		_usingAbility = true
		if mana >= value.spell_cost:
			set_mana(value.spellCost)
			match value.specialAbilityType:
				Spell.SpecialAbilityType.AREA:
					abilities.AreaAttack(value)
					cooldown()
				Spell.SpecialAbilityType.RANGED:
					abilities.Ranged(value);
					cooldown()
				Spell.SpecialAbilityType.SELF:
					abilities.Self(value)
					cooldown()
				Spell.SpecialAbilityType.SUMMON:
					abilities.Summon(value);
					cooldown()
		elif mana < value.spell_cost:
			#DialogueManager.BarkString("I don't have enough Aura", transform.parent);
			pass
		_usingAbility = false
	else:
		pass


func basic_ability(ability: String):
	_usingAbility = true
	if targets.size > 0:
		set_battle_mode(true)
	match ability:
				"swing":
					abilities.swing()
				"kick":
					abilities.kick()
				"dodge":
					abilities.dodge()
				"parry":
					abilities.parry()
				"block":
					if !_usingAbility && _isBlocking:
						abilities.block()
	cooldown()
	_usingAbility = false


func move_towards_target(target):
	if position.distance_to(target.position) > 0.5 && position.distance_to(target.position) < 3:
		start_dashing()
		player_physics.move_and_slide((target.position - position).normalized() * 5, Vector3(0, 1, 0), false, 4, 0.785398)
		aim_object.look_at(target.position, Vector3(0, 1, 0))

func start_dashing():
	_dashing = true
	cooldown()
	_dashing = false

func set_aim_camera(on):
	if aim_camera:
		og_aim = aim_camera.rotation
		on_target_select_mode.emit(on)
		if on:
			aim_camera.current = true
			inspect_camera.current = false
			#disable movement
			aim_camera.look_at(aim_object.position)
			_isAiming = true;
		else:
			aim_camera.current = false
			inspect_camera.current = true
			#enable movement
			aim_camera.rotation = og_aim
			_isAiming = false
	else:
		pass

func set_tactical_mode(on):
	_tacticalMode = on
	on_tactical_trigger.emit(on)
	if on:
		set_aim_camera(true)
		set_defense()
		Engine.set_time_scale(slowMotionTime)
		get_tree().call_group("Enemies", "set_tactical_mode", on)
	else:
		set_aim_camera(false)
		Engine.set_time_scale(1)
		get_tree().call_group("Enemies", "set_tactical_mode", on)

func set_defense():
	character_resource.defense = 0
	for item in InventorySystem.equipped.keys():
		character_resource.defense += item

func nearest_target_to_center():
	var distances = []
	for target in targets:
		distances.append(position.distance_to(target.position))
	var minDistance = distances[0]
	var index = 0
	for i in range(1, distances.size()):
		if distances[i] < minDistance:
			minDistance = distances[i]
			index = i
	return index

func clear_targets():
	targets.clear()

func set_battle_mode(value):
	_battleMode = value
	on_battle_mode.emit(value)
	set_tactical_mode(value)

func cooldown():
	await get_tree().create_timer(1.0).timeout
