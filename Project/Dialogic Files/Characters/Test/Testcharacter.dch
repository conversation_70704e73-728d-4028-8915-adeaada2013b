{
"@path": "res://addons/dialogic/Resources/character.gd",
"@subpath": NodePath(""),
&"_translation_id": "",
&"color": Color(1, 1, 1, 1),
&"custom_info": {
"sound_mood_default": "",
"sound_moods": {},
"style": "VN Style"
},
&"default_portrait": "Portrait",
&"description": "Is a test",
&"display_name": "Testcharacter",
&"mirror": false,
&"nicknames": ["Testy"],
&"offset": Vector2(0, 0),
&"portraits": {
"Portrait": {
"export_overrides": {
"image": "\"res://Project/Sprites/Ricky.png\""
},
"mirror": false,
"offset": Vector2(0, 0),
"scale": 1,
"scene": ""
}
},
&"scale": 1.0
}