[gd_resource type="Resource" script_class="DialogicGlossary" load_steps=2 format=3 uid="uid://b4plevwrq34rb"]

[ext_resource type="Script" uid="uid://c31hwqy83swwc" path="res://addons/dialogic/Modules/Glossary/glossary_resource.gd" id="1_ofhu3"]

[resource]
script = ExtResource("1_ofhu3")
entries = {
"Aura": {
"alternatives": ["mana"],
"case_sensitive": false,
"name": "Aura"
},
"Lucid Mode": {
"alternatives": ["lucid"],
"case_sensitive": false,
"name": "Lucid Mode"
},
"lucid": "Lucid Mode",
"mana": "Aura"
}
enabled = true
_translation_id = ""
_translation_keys = {}
