[gd_resource type="Resource" script_class="DialogicStyle" load_steps=20 format=3 uid="uid://b3sck6dmjpyot"]

[ext_resource type="Script" uid="uid://cwwf7uaorryop" path="res://addons/dialogic/Resources/dialogic_style_layer.gd" id="1_fi4td"]
[ext_resource type="PackedScene" uid="uid://c1k5m0w3r40xf" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_FullBackground/full_background_layer.tscn" id="2_p6lj2"]
[ext_resource type="PackedScene" uid="uid://cy1y14inwkplb" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Portraits/vn_portrait_layer.tscn" id="3_c4lln"]
[ext_resource type="PackedScene" uid="uid://cn674foxwedqu" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Input/full_advance_input_layer.tscn" id="4_86xi5"]
[ext_resource type="PackedScene" uid="uid://bquja8jyk8kbr" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_layer.tscn" id="5_t0ifu"]
[ext_resource type="PackedScene" uid="uid://dsbwnp5hegnu3" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_Glossary/glossary_popup_layer.tscn" id="6_vhdrv"]
[ext_resource type="PackedScene" uid="uid://dhk6j6eb6e3q" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Choices/vn_choice_layer.tscn" id="7_kbdvm"]
[ext_resource type="PackedScene" uid="uid://cvgf4c6gg0tsy" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_TextInput/text_input_layer.tscn" id="8_bm6j1"]
[ext_resource type="PackedScene" uid="uid://lx24i8fl6uo" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_History/history_layer.tscn" id="9_1qw14"]
[ext_resource type="Script" uid="uid://b4erugnrcy7c6" path="res://addons/dialogic/Resources/dialogic_style.gd" id="10_pahai"]

[sub_resource type="Resource" id="Resource_l52f8"]
script = ExtResource("1_fi4td")
overrides = {}

[sub_resource type="Resource" id="Resource_jaovj"]
script = ExtResource("1_fi4td")
scene = ExtResource("2_p6lj2")
overrides = {}

[sub_resource type="Resource" id="Resource_lvfop"]
script = ExtResource("1_fi4td")
scene = ExtResource("3_c4lln")
overrides = {
"portrait_size_mode": "0"
}

[sub_resource type="Resource" id="Resource_qd7c4"]
script = ExtResource("1_fi4td")
scene = ExtResource("4_86xi5")
overrides = {}

[sub_resource type="Resource" id="Resource_h1tq8"]
script = ExtResource("1_fi4td")
scene = ExtResource("5_t0ifu")
overrides = {
"box_size": "Vector2(750, 275)"
}

[sub_resource type="Resource" id="Resource_tp7li"]
script = ExtResource("1_fi4td")
scene = ExtResource("6_vhdrv")
overrides = {}

[sub_resource type="Resource" id="Resource_q556q"]
script = ExtResource("1_fi4td")
scene = ExtResource("7_kbdvm")
overrides = {}

[sub_resource type="Resource" id="Resource_xvweg"]
script = ExtResource("1_fi4td")
scene = ExtResource("8_bm6j1")
overrides = {}

[sub_resource type="Resource" id="Resource_s31ku"]
script = ExtResource("1_fi4td")
scene = ExtResource("9_1qw14")
overrides = {}

[resource]
script = ExtResource("10_pahai")
name = "VN Style"
layer_list = Array[String](["10", "11", "12", "13", "14", "15", "16", "17"])
layer_info = {
"": SubResource("Resource_l52f8"),
"10": SubResource("Resource_jaovj"),
"11": SubResource("Resource_lvfop"),
"12": SubResource("Resource_qd7c4"),
"13": SubResource("Resource_h1tq8"),
"14": SubResource("Resource_tp7li"),
"15": SubResource("Resource_q556q"),
"16": SubResource("Resource_xvweg"),
"17": SubResource("Resource_s31ku")
}
base_overrides = {}
layers = Array[ExtResource("1_fi4td")]([])
metadata/_latest_layer = "18"
