[gd_resource type="StandardMaterial3D" load_steps=3 format=3 uid="uid://cyw6vaooc3thi"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_0ogxg"]
noise_type = 3
frequency = 0.0089
fractal_gain = 0.565
fractal_weighted_strength = 0.33

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_iww1c"]
width = 1024
height = 1024
seamless = true
normalize = false
noise = SubResource("FastNoiseLite_0ogxg")

[resource]
albedo_color = Color(0.537011, 0.537011, 0.537011, 1)
roughness_texture = SubResource("NoiseTexture2D_iww1c")
uv1_scale = Vector3(0.05, 0.05, 0.05)
uv1_triplanar = true
uv1_world_triplanar = true
