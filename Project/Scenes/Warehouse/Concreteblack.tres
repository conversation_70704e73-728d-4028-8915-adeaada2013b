[gd_resource type="StandardMaterial3D" load_steps=3 format=3 uid="uid://cy001pj0gwh51"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_0ogxg"]
noise_type = 3
frequency = 0.0428
fractal_gain = 0.565
fractal_weighted_strength = 0.33

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_iww1c"]
width = 1024
height = 1024
seamless = true
as_normal_map = true
noise = SubResource("FastNoiseLite_0ogxg")

[resource]
albedo_color = Color(0.537011, 0.537011, 0.537011, 1)
roughness_texture = SubResource("NoiseTexture2D_iww1c")
uv1_scale = Vector3(15, 15, 15)
