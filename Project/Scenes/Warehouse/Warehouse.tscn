[gd_scene load_steps=15 format=3 uid="uid://bcjo0mlwffgg8"]

[ext_resource type="PackedScene" uid="uid://xgahq2g7ls66" path="res://Project/Models/vaulted arch 8.glb" id="1_0dyxf"]
[ext_resource type="LightmapGIData" uid="uid://ck5ci1exk4o3f" path="res://Project/Scenes/Warehouse/Warehouse.lmbake" id="1_0ogxg"]
[ext_resource type="PackedScene" uid="uid://drjqcoul7pinv" path="res://WIP files/Player/Player.tscn" id="2_0dyxf"]
[ext_resource type="Material" uid="uid://cy001pj0gwh51" path="res://Project/Scenes/Warehouse/Concreteblack.tres" id="3_a2u6p"]
[ext_resource type="Material" uid="uid://cyw6vaooc3thi" path="res://Project/Scenes/Warehouse/Concrete.tres" id="4_vyu6s"]
[ext_resource type="Material" uid="uid://ds6wi21vht2li" path="res://Project/Scenes/Warehouse/Iron.tres" id="5_13xym"]
[ext_resource type="Material" uid="uid://bye0jdo4hx3pe" path="res://Project/Scenes/Warehouse/transparent.tres" id="5_vyu6s"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_iww1c"]
emission_enabled = true
emission = Color(1, 1, 1, 1)
emission_energy_multiplier = 6.08

[sub_resource type="PhysicalSkyMaterial" id="PhysicalSkyMaterial_iww1c"]
rayleigh_color = Color(0.214627, 0.142991, 0.252074, 1)

[sub_resource type="Sky" id="Sky_htedn"]
sky_material = SubResource("PhysicalSkyMaterial_iww1c")

[sub_resource type="Environment" id="Environment_a2u6p"]
background_mode = 2
sky = SubResource("Sky_htedn")
sdfgi_use_occlusion = true
sdfgi_cascades = 7
sdfgi_max_distance = 1638.4
sdfgi_y_scale = 0
volumetric_fog_enabled = true
volumetric_fog_density = 0.0

[sub_resource type="FastNoiseLite" id="FastNoiseLite_a2u6p"]
noise_type = 5
frequency = 0.0216
fractal_gain = 0.555
domain_warp_type = 1

[sub_resource type="NoiseTexture3D" id="NoiseTexture3D_vyu6s"]
width = 512
height = 512
depth = 512
noise = SubResource("FastNoiseLite_a2u6p")

[sub_resource type="FogMaterial" id="FogMaterial_13xym"]
density = 0.5826
albedo = Color(0.49, 0.3528, 0.38024, 1)
height_falloff = 4.5642
density_texture = SubResource("NoiseTexture3D_vyu6s")

[node name="Rave" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.0104718, 0.999945, 0, -0.999945, 0.0104718, 0, 0, -52.6074)
light_color = Color(0.750856, 0.734983, 0.841212, 1)
light_energy = 0.434
shadow_enabled = true
shadow_reverse_cull_face = true
directional_shadow_blend_splits = true

[node name="LightmapGI" type="LightmapGI" parent="."]
layers = 3
quality = 2
supersampling = true
directional = true
shadowmask_mode = 1
interior = true
denoiser_strength = 0.2
generate_probes_subdiv = 3
light_data = ExtResource("1_0ogxg")

[node name="Left spotlights" type="Node3D" parent="."]

[node name="SpotLight3D" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 25.5111, 16.4293, -26.6589)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D2" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 34.511, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D3" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 43.0659, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D4" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, -1.09823, 16.4293, -26.6589)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D7" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, -10.2731, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D5" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 7.90167, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D6" type="SpotLight3D" parent="Left spotlights"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 16.4566, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="Left spotlights2" type="Node3D" parent="."]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 33.348, -0.763289, -99.015)

[node name="SpotLight3D" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 25.5111, 16.4293, -26.6589)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D2" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 34.511, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D3" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 43.0659, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D4" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, -1.09823, 16.4293, -26.6589)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D7" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, -10.2731, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D5" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 7.90167, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="SpotLight3D6" type="SpotLight3D" parent="Left spotlights2"]
transform = Transform3D(1, 0, 0, 0, 0.867696, 0.497095, 0, -0.497095, 0.867696, 16.4566, 16.429, -26.659)
light_color = Color(0.6768, 0.5472, 0.72, 1)
light_energy = 3.549
light_bake_mode = 1
shadow_enabled = true
spot_range = 39.271
spot_attenuation = 0.12
spot_angle = 13.15

[node name="vaulted arch 8" parent="." instance=ExtResource("1_0dyxf")]

[node name="star" parent="vaulted arch 8/Sketchfab_model_002/8c21a32d77b4451596e63b230e25ea51_fbx/RootNode/pCylinder1" index="0"]
gi_mode = 2
surface_material_override/0 = SubResource("StandardMaterial3D_iww1c")

[node name="roof beams_007" parent="vaulted arch 8" index="20"]
material_override = ExtResource("3_a2u6p")

[node name="room box" parent="vaulted arch 8" index="21"]
material_override = ExtResource("4_vyu6s")
cast_shadow = 2

[node name="main roof_001" parent="vaulted arch 8" index="22"]
transform = Transform3D(3195.15, 0, 0, 0, 4.93697, 0, 0, 0, 9.64278, 42.9732, 14.6117, -50.2964)
cast_shadow = 2
surface_material_override/0 = ExtResource("5_vyu6s")
surface_material_override/1 = ExtResource("4_vyu6s")

[node name="WINDOW straightened_ right" parent="vaulted arch 8" index="23"]
material_override = ExtResource("5_13xym")

[node name="room arches_001" parent="vaulted arch 8" index="24"]
material_override = ExtResource("4_vyu6s")

[node name="room arches_002" parent="vaulted arch 8" index="25"]
material_override = ExtResource("4_vyu6s")
cast_shadow = 2

[node name="room arches " parent="vaulted arch 8" index="26"]
cast_shadow = 2

[node name="roof cross beams" parent="vaulted arch 8" index="28"]
material_override = ExtResource("4_vyu6s")

[node name="path1_006" parent="vaulted arch 8" index="29"]
material_override = ExtResource("3_a2u6p")

[node name="path1_009" parent="vaulted arch 8" index="30"]
transform = Transform3D(155.717, 0, 0, 0, 155.717, 0, 0, 0, 155.717, -24.759, 14.5544, -38.2296)
material_override = ExtResource("3_a2u6p")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_a2u6p")

[node name="FogVolume" type="FogVolume" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12.022, 0.533, -52.558)
size = Vector3(70.27, 2, 55.89)
material = SubResource("FogMaterial_13xym")

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.11559, 3.59471, -37.0886)
light_color = Color(1, 0.52, 0.76, 1)
light_energy = 0.4
light_specular = 0.25
omni_range = 15.299

[node name="OmniLight3D2" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.11559, 3.59471, -63.8127)
light_color = Color(1, 0.52, 0.76, 1)
light_energy = 0.4
light_specular = 0.25
omni_range = 15.299

[node name="Player" parent="." instance=ExtResource("2_0dyxf")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23.6008, -4.76837e-07, -54.412)

[editable path="vaulted arch 8"]
