[remap]

importer="2d_array_texture"
type="CompressedTexture2DArray"
uid="uid://b7enr68lbay5y"
path.bptc="res://.godot/imported/Warehouse.exr-3938fe0eca81265e811af4b2a292b0ad.bptc.ctexarray"
path.astc="res://.godot/imported/Warehouse.exr-3938fe0eca81265e811af4b2a292b0ad.astc.ctexarray"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://Project/Scenes/Warehouse/Warehouse.exr"
dest_files=["res://.godot/imported/Warehouse.exr-3938fe0eca81265e811af4b2a292b0ad.bptc.ctexarray", "res://.godot/imported/Warehouse.exr-3938fe0eca81265e811af4b2a292b0ad.astc.ctexarray"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/channel_pack=1
mipmaps/generate=false
mipmaps/limit=-1
slices/horizontal=1
slices/vertical=12
