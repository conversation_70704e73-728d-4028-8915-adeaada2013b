[remap]

importer="2d_array_texture"
type="CompressedTexture2DArray"
uid="uid://cj7c47r34iwlh"
path.s3tc="res://.godot/imported/Warehouse_shadow.png-421b272e6aa916ebc066581a98ee8ada.s3tc.ctexarray"
path.etc2="res://.godot/imported/Warehouse_shadow.png-421b272e6aa916ebc066581a98ee8ada.etc2.ctexarray"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://Project/Scenes/Warehouse/Warehouse_shadow.png"
dest_files=["res://.godot/imported/Warehouse_shadow.png-421b272e6aa916ebc066581a98ee8ada.s3tc.ctexarray", "res://.godot/imported/Warehouse_shadow.png-421b272e6aa916ebc066581a98ee8ada.etc2.ctexarray"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/channel_pack=1
mipmaps/generate=false
mipmaps/limit=-1
slices/horizontal=1
slices/vertical=3
