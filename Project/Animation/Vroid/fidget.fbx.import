[remap]

importer="animation_library"
importer_version=1
type="AnimationLibrary"
uid="uid://c2nsu4kdwfegx"
path="res://.godot/imported/fidget.fbx-bec3f8c6290f70da2c2eb86b02df99fa.res"

[deps]

source_file="res://Project/Animation/Vroid/fidget.fbx"
dest_files=["res://.godot/imported/fidget.fbx-bec3f8c6290f70da2c2eb86b02df99fa.res"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=true
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=true
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={
"nodes": {
"PATH:Skeleton3D": {
"retarget/bone_map": Resource("res://Project/Animation/Vroid/bonemap.tres"),
"retarget/rest_fixer/fix_silhouette/enable": true,
"retarget/rest_fixer/fix_silhouette/threshold": 5.0
}
}
}
fbx/importer=0
fbx/allow_geometry_helper_nodes=false
fbx/embedded_image_handling=1
