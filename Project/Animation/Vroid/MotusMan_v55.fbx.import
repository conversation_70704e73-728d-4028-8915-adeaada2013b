[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://b3d8snsk2im28"
path="res://.godot/imported/MotusMan_v55.fbx-229738eac88401f2bbf8ddf37b0b2292.scn"

[deps]

source_file="res://Project/Animation/Vroid/MotusMan_v55.fbx"
dest_files=["res://.godot/imported/MotusMan_v55.fbx-229738eac88401f2bbf8ddf37b0b2292.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=true
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=true
import_script/path=""
_subresources={
"nodes": {
"PATH:Skeleton3D": {
"rest_pose/external_animation_library": null,
"retarget/bone_map": Object(BoneMap,"resource_local_to_scene":false,"resource_name":"","profile":Object(SkeletonProfileHumanoid,"resource_local_to_scene":false,"resource_name":"","root_bone":&"Root","scale_base_bone":&"Hips","group_size":4,"bone_size":56,"script":null)
,"bonemap":null,"bone_map/Root":&"Root","bone_map/Hips":&"Hips","bone_map/Spine":&"Spine","bone_map/Chest":&"Spine1","bone_map/UpperChest":&"Spine2","bone_map/Neck":&"Neck","bone_map/Head":&"Head","bone_map/LeftEye":&"","bone_map/RightEye":&"","bone_map/Jaw":&"","bone_map/LeftShoulder":&"LeftShoulder","bone_map/LeftUpperArm":&"LeftArm","bone_map/LeftLowerArm":&"LeftForeArm","bone_map/LeftHand":&"LeftHand","bone_map/LeftThumbMetacarpal":&"LeftHandThumb1","bone_map/LeftThumbProximal":&"LeftHandThumb2","bone_map/LeftThumbDistal":&"LeftHandThumb3","bone_map/LeftIndexProximal":&"LeftHandIndex1","bone_map/LeftIndexIntermediate":&"LeftHandIndex2","bone_map/LeftIndexDistal":&"LeftHandIndex3","bone_map/LeftMiddleProximal":&"LeftHandMiddle1","bone_map/LeftMiddleIntermediate":&"LeftHandMiddle2","bone_map/LeftMiddleDistal":&"LeftHandMiddle3","bone_map/LeftRingProximal":&"LeftHandRing1","bone_map/LeftRingIntermediate":&"LeftHandRing2","bone_map/LeftRingDistal":&"LeftHandRing3","bone_map/LeftLittleProximal":&"LeftHandPinky1","bone_map/LeftLittleIntermediate":&"LeftHandPinky2","bone_map/LeftLittleDistal":&"LeftHandPinky3","bone_map/RightShoulder":&"RightShoulder","bone_map/RightUpperArm":&"RightArm","bone_map/RightLowerArm":&"RightForeArm","bone_map/RightHand":&"RightHand","bone_map/RightThumbMetacarpal":&"RightHandThumb1","bone_map/RightThumbProximal":&"RightHandThumb2","bone_map/RightThumbDistal":&"RightHandThumb3","bone_map/RightIndexProximal":&"RightHandIndex1","bone_map/RightIndexIntermediate":&"RightHandIndex2","bone_map/RightIndexDistal":&"RightHandIndex3","bone_map/RightMiddleProximal":&"RightHandMiddle1","bone_map/RightMiddleIntermediate":&"RightHandMiddle2","bone_map/RightMiddleDistal":&"RightHandMiddle3","bone_map/RightRingProximal":&"RightHandRing1","bone_map/RightRingIntermediate":&"RightHandRing2","bone_map/RightRingDistal":&"RightHandRing3","bone_map/RightLittleProximal":&"RightHandPinky1","bone_map/RightLittleIntermediate":&"RightHandPinky2","bone_map/RightLittleDistal":&"RightHandPinky3","bone_map/LeftUpperLeg":&"LeftUpLeg","bone_map/LeftLowerLeg":&"LeftLeg","bone_map/LeftFoot":&"LeftFoot","bone_map/LeftToes":&"LeftToeBase","bone_map/RightUpperLeg":&"RightUpLeg","bone_map/RightLowerLeg":&"RightLeg","bone_map/RightFoot":&"RightFoot","bone_map/RightToes":&"RightToeBase","script":null)

}
}
}
fbx/importer=0
fbx/allow_geometry_helper_nodes=false
fbx/embedded_image_handling=1
