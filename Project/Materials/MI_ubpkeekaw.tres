[gd_resource type="StandardMaterial3D" load_steps=4 format=3 uid="uid://dr12dj05vcr01"]

[ext_resource type="Texture2D" uid="uid://4vpytem77r53" path="res://Project/Materials/MI_ubpkeekaw_BaseColor.png" id="1_75qs7"]
[ext_resource type="Texture2D" uid="uid://cdv27jie2opbr" path="res://Project/Materials/MI_ubpkeekaw_MetallicRoughness.png" id="2_fgbxd"]
[ext_resource type="Texture2D" uid="uid://c8b5xgow0yev" path="res://Project/Materials/MI_ubpkeekaw_Normal.png" id="3_cc0je"]

[resource]
resource_name = "MI_ubpkeekaw"
albedo_texture = ExtResource("1_75qs7")
metallic = 1.0
metallic_texture = ExtResource("2_fgbxd")
metallic_texture_channel = 2
roughness_texture = ExtResource("2_fgbxd")
roughness_texture_channel = 1
normal_enabled = true
normal_texture = ExtResource("3_cc0je")
uv1_scale = Vector3(20, 20, 20)
