[gd_resource type="ORMMaterial3D" load_steps=4 format=3 uid="uid://benymayxfkx6m"]

[ext_resource type="Texture2D" uid="uid://be7lkici8pmh7" path="res://Project/Materials/MM Materials/Blue Tile_albedo.png" id="1"]
[ext_resource type="Texture2D" uid="uid://db8ps4yej4o1a" path="res://Project/Materials/MM Materials/Blue Tile_normal.png" id="3"]
[ext_resource type="Texture2D" uid="uid://k7w1if05i78f" path="res://Project/Materials/MM Materials/Blue Tile_orm.png" id="3_4wsws"]

[resource]
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("1")
orm_texture = ExtResource("3_4wsws")
emission = Color(1, 1, 1, 1)
emission_operator = 1
emission_on_uv2 = true
normal_enabled = true
normal_texture = ExtResource("3")
backlight = Color(0.796243, 0.796243, 0.796243, 1)
backlight_texture = ExtResource("3_4wsws")
refraction_enabled = true
uv1_scale = Vector3(6.53, 6.53, 6.53)
uv2_scale = Vector3(2.5, 2.5, 2.5)
uv2_offset = Vector3(0, 2.5, 0)
