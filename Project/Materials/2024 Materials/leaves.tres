[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://cbkq868l1wil6"]

[ext_resource type="Shader" uid="uid://vvpl7frq8pg8" path="res://Project/Materials/2024 Materials/SHADERS/foliage_shader.tres" id="1_s6wxx"]

[resource]
render_priority = 0
shader = ExtResource("1_s6wxx")
shader_parameter/triplanar_scale = Vector3(1, 1, 1)
shader_parameter/triplanar_offset = null
shader_parameter/triplanar_sharpness = 0.5
