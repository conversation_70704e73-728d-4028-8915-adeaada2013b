[gd_resource type="VisualShader" load_steps=14 format=3 uid="uid://vvpl7frq8pg8"]

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_cvc5i"]
parameter_name = "Input"

[sub_resource type="VisualShaderNodeTextureParameterTriplanar" id="VisualShaderNodeTextureParameterTriplanar_ul1dq"]
parameter_name = "TextureParameterTriplanar"
color_default = 1

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_7o4sr"]
input_name = "normal"

[sub_resource type="VisualShaderNodeVectorFunc" id="VisualShaderNodeVectorFunc_juf8o"]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_2bpft"]
input_name = "node_position_world"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_j4ncq"]
input_name = "uv"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_sj8gh"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(1, 1, 1)]
operator = 2

[sub_resource type="VisualShaderNodeTransformVecMult" id="VisualShaderNodeTransformVecMult_grc34"]
default_input_values = [0, Transform3D(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1), 1, Vector3(0, 0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_rrm18"]
input_name = "view_matrix"

[sub_resource type="VisualShaderNodeTransformVecMult" id="VisualShaderNodeTransformVecMult_w6ckl"]
operator = 1

[sub_resource type="VisualShaderNodeTransformVecMult" id="VisualShaderNodeTransformVecMult_bgdy8"]
operator = 1

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_ppjqn"]
input_name = "model_matrix"

[sub_resource type="VisualShaderNodeRemap" id="VisualShaderNodeRemap_ah8rd"]
default_input_values = [1, 0.0, 2, 1.0, 3, -1.0, 4, 1.0]

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;


// Varyings
varying mat4 var_Scale;

uniform sampler2D TextureParameterTriplanar : hint_default_black;


// TextureParameterTriplanar
	vec4 triplanar_texture(sampler2D p_sampler, vec3 p_weights, vec3 p_triplanar_pos) {
		vec4 samp = vec4(0.0);
		samp += texture(p_sampler, p_triplanar_pos.xy) * p_weights.z;
		samp += texture(p_sampler, p_triplanar_pos.xz) * p_weights.y;
		samp += texture(p_sampler, p_triplanar_pos.zy * vec2(-1.0, 1.0)) * p_weights.x;
		return samp;
	}

	uniform vec3 triplanar_scale = vec3(1.0, 1.0, 1.0);
	uniform vec3 triplanar_offset;
	uniform float triplanar_sharpness = 0.5;

	varying vec3 triplanar_power_normal;
	varying vec3 triplanar_pos;

void vertex() {
// TextureParameterTriplanar
	{
		triplanar_power_normal = pow(abs(NORMAL), vec3(triplanar_sharpness));
		triplanar_power_normal /= dot(triplanar_power_normal, vec3(1.0));
		triplanar_pos = VERTEX * triplanar_scale + triplanar_offset;
		triplanar_pos *= vec3(1.0, -1.0, 1.0);
	}
// Input:6
	mat4 n_out6p0 = MODEL_MATRIX;


// Input:3
	mat4 n_out3p0 = VIEW_MATRIX;


// Input:2
	vec2 n_out2p0 = UV;


	float n_out7p0;
// Remap:7
	float n_in7p1 = 0.00000;
	float n_in7p2 = 1.00000;
	float n_in7p3 = -1.00000;
	float n_in7p4 = 1.00000;
	{
		float __input_range = n_in7p2 - n_in7p1;
		float __output_range = n_in7p4 - n_in7p3;
		n_out7p0 = n_in7p3 + __output_range * ((n_out2p0.x - n_in7p1) / __input_range);
	}


// TransformVectorMult:4
	vec3 n_out4p0 = (vec4(vec3(n_out7p0), 1.0) * n_out3p0).xyz;


// TransformVectorMult:5
	vec3 n_out5p0 = (vec4(n_out4p0, 1.0) * n_out6p0).xyz;


// TransformVectorMult:24
	mat4 n_in24p0 = mat4(vec4(1.00000, 1.00000, 1.00000, 0.0), vec4(1.00000, 1.00000, 1.00000, 0.0), vec4(1.00000, 1.00000, 1.00000, 0.0), vec4(1.00000, 1.00000, 1.00000, 1.0));
	vec3 n_out24p0 = (n_in24p0 * vec4(n_out5p0, 1.0)).xyz;


// Output:0
	VERTEX = n_out24p0;


	var_Scale = mat4(1.0);
}

void fragment() {
// TextureParameterTriplanar:4
	vec4 n_out4p0 = triplanar_texture(TextureParameterTriplanar, triplanar_power_normal, triplanar_pos);


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);


}
"
varyings/Scale = "0,7"
nodes/vertex/0/position = Vector2(920, -20)
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_j4ncq")
nodes/vertex/2/position = Vector2(-1207.98, 141.096)
nodes/vertex/3/node = SubResource("VisualShaderNodeInput_rrm18")
nodes/vertex/3/position = Vector2(-887.976, 481.096)
nodes/vertex/4/node = SubResource("VisualShaderNodeTransformVecMult_w6ckl")
nodes/vertex/4/position = Vector2(-547.976, 241.096)
nodes/vertex/5/node = SubResource("VisualShaderNodeTransformVecMult_bgdy8")
nodes/vertex/5/position = Vector2(-227.976, 301.096)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_ppjqn")
nodes/vertex/6/position = Vector2(-527.976, 521.096)
nodes/vertex/7/node = SubResource("VisualShaderNodeRemap_ah8rd")
nodes/vertex/7/position = Vector2(-827.976, 161.096)
nodes/vertex/10/node = SubResource("VisualShaderNodeInput_7o4sr")
nodes/vertex/10/position = Vector2(-520, 20)
nodes/vertex/12/node = SubResource("VisualShaderNodeVectorFunc_juf8o")
nodes/vertex/12/position = Vector2(-140, -20)
nodes/vertex/19/node = SubResource("VisualShaderNodeInput_2bpft")
nodes/vertex/19/position = Vector2(240, -40)
nodes/vertex/23/node = SubResource("VisualShaderNodeVectorOp_sj8gh")
nodes/vertex/23/position = Vector2(80, -20)
nodes/vertex/24/node = SubResource("VisualShaderNodeTransformVecMult_grc34")
nodes/vertex/24/position = Vector2(540, 40)
nodes/vertex/connections = PackedInt32Array(2, 0, 7, 0, 3, 0, 4, 0, 7, 0, 4, 1, 4, 0, 5, 1, 6, 0, 5, 0, 10, 0, 12, 0, 12, 0, 23, 0, 5, 0, 24, 1, 24, 0, 0, 0)
nodes/fragment/0/position = Vector2(1080, 80)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture2DParameter_cvc5i")
nodes/fragment/3/position = Vector2(80, 280)
nodes/fragment/4/node = SubResource("VisualShaderNodeTextureParameterTriplanar_ul1dq")
nodes/fragment/4/position = Vector2(460, 180)
nodes/fragment/connections = PackedInt32Array(4, 0, 0, 0)
