shader_type spatial;
render_mode depth_prepass_alpha, cull_disabled, world_vertex_coords;

uniform sampler2D texture_albedo : source_color;
uniform vec4 transmission : source_color;

uniform float sway_speed = 1.0;
uniform float sway_strength = 0.05;
uniform float sway_phase_len = 8.0;

uniform float billboard_size = 1.0;
uniform vec3 inflate_direction = vec3(0.0, 1.0, 0.0); // Inflate direction (normal vector)
uniform float inflate_factor = 1.0; // How much to inflate

void vertex() {
	    // Apply the UV remap
    vec2 remapped_uv = UV * 2.0 - 1.0; // Remapping UV similar to Unity


    // Get the object position in world space
    vec3 object_world_position = NODE_POSITION_WORLD;

    // Billboard alignment logic (rotate vertices to face the camera)
    vec3 camera_position = CAMERA_DIRECTION_WORLD;
    vec3 to_camera = normalize(camera_position - object_world_position);

   // Align the billboard vertices to face the camera
    mat3 view_matrix = mat3(VIEW_MATRIX);
    vec3 aligned_vertex = view_matrix * VERTEX;

    // Normalize the billboard scale based on object scale
    vec3 object_scale =vec3(length(MODEL_MATRIX[0].xyz), length(MODEL_MATRIX[1].xyz), length(MODEL_MATRIX[2].xyz));
    float normalized_scale = 3.0* length(object_scale);
    aligned_vertex /= normalized_scale;

    // Apply size scaling (billboard size)
    aligned_vertex *= billboard_size;

    // Apply inflation effect
    vec3 normal_vector = normalize(NORMAL);
    aligned_vertex += normal_vector * inflate_factor;

    // Output transformed vertex position
    VERTEX = (MODEL_MATRIX * vec4(aligned_vertex, 1.0)).xyz;

    // Output transformed vertex position
    float strength = COLOR.r * sway_strength;
    VERTEX.x += sin(VERTEX.x * sway_phase_len * 1.123 + TIME * sway_speed) * strength;
    VERTEX.y += sin(VERTEX.y * sway_phase_len + TIME * sway_speed * 1.12412) * strength;
    VERTEX.z += sin(VERTEX.z * sway_phase_len * 0.9123 + TIME * sway_speed * 1.3123) * strength;
}

void fragment() {
    vec4 albedo_tex = texture(texture_albedo, UV);
    ALBEDO = albedo_tex.rgb;
    ALPHA = albedo_tex.a;
    METALLIC = 0.0;
    ROUGHNESS = 1.0;
	SPECULAR = 0.1;
    SSS_TRANSMITTANCE_COLOR = transmission;
}