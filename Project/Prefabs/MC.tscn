[gd_scene load_steps=10 format=3 uid="uid://ckavprxhhm8kj"]

[ext_resource type="Script" uid="uid://bs14y23xsbeeg" path="res://Project/Systems/Combat/CombatSystem.gd" id="1_77w6k"]
[ext_resource type="Resource" uid="uid://cduh1cjgwemem" path="res://Project/Systems/Resources/Characters/playerrsc.tres" id="2_8idb3"]
[ext_resource type="PackedScene" uid="uid://c7aquabrd8ye5" path="res://Project/UI/CombatUI.tscn" id="3_3g6pe"]
[ext_resource type="Script" uid="uid://cc056fx2t4in4" path="res://Project/Systems/Inventory/InventorySystem.gd" id="4_n8cwn"]
[ext_resource type="Script" uid="uid://blqs701n84heu" path="res://Project/Systems/Inventory/InventoryManager.gd" id="5_dwgky"]
[ext_resource type="PackedScene" uid="uid://bpn5fdnfjplil" path="res://Project/UI/held_slot.tscn" id="6_7r05s"]
[ext_resource type="PackedScene" uid="uid://dyi2rn480l576" path="res://Project/UI/InventoryUI.tscn" id="7_jh2u8"]
[ext_resource type="Script" uid="uid://cpj4wrq4xmhrg" path="res://Project/Systems/Inventory/InfoPanelManager.gd" id="8_qk5do"]
[ext_resource type="Script" uid="uid://cbxvcv7eh2lqk" path="res://Project 2024/Scenes/Tests/pause_handler.gd" id="9_jj3jn"]

[node name="Player" type="Node3D"]

[node name="CombatSystem" type="Node3D" parent="." node_paths=PackedStringArray("player", "aim_object", "inspect_camera", "local_inventory_system") groups=["Systems"]]
script = ExtResource("1_77w6k")
player = NodePath("")
character_resource = ExtResource("2_8idb3")
aim_object = NodePath("")
inspect_camera = NodePath("")
local_inventory_system = NodePath("")

[node name="CombatUI" parent="CombatSystem" node_paths=PackedStringArray("local_combat_system") instance=ExtResource("3_3g6pe")]
visible = false
local_combat_system = NodePath("..")

[node name="Inventory" type="Node" parent="."]
process_mode = 1
script = ExtResource("4_n8cwn")
player_resource = ExtResource("2_8idb3")

[node name="Inventory Manager" type="Node" parent="Inventory" node_paths=PackedStringArray("inventory_parent", "equip_parent", "info_panel", "local_inventory_system", "head_slot", "upper_body_slot", "lower_body_slot", "hands_slot", "feet_slot", "accessory_one_slot", "accessory_two_slot", "combat")]
script = ExtResource("5_dwgky")
slot_prefab = ExtResource("6_7r05s")
inventory_parent = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer/HeldItemsPanel/CenterContainer/GridContainer")
equip_parent = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer")
info_panel = NodePath("../InventoryUI/MarginContainer/Control/EquipInfoPanelContainer")
local_inventory_system = NodePath("..")
head_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer/Head")
upper_body_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer/Upper")
lower_body_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer/Lower")
hands_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2/Hands")
feet_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer/Shoes")
accessory_one_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2/Acc1")
accessory_two_slot = NodePath("../InventoryUI/MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2/Acc2")
combat = NodePath("")

[node name="InventoryUI" parent="Inventory" instance=ExtResource("7_jh2u8")]
unique_name_in_owner = true
process_mode = 1
visible = false
offset_right = 0.0
offset_bottom = 0.0

[node name="EquipInfoPanelContainer" parent="Inventory/InventoryUI/MarginContainer/Control" index="1" node_paths=PackedStringArray("titleText", "displayImage", "descriptionText", "typeText", "classText", "damageText", "spellCostText")]
script = ExtResource("8_qk5do")
titleText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/CenterContainer/VBoxContainer/TitleLabel")
displayImage = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/CenterContainer/VBoxContainer/DisplayImage")
descriptionText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/DescriptionLabel")
typeText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/TypeLabel")
classText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/ClassLabel")
damageText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/DamageLabel")
spellCostText = NodePath("EquipInfoPanel/MarginContainer/VBoxContainer/SpellCostLabel")

[node name="SubViewport" parent="Inventory/InventoryUI/MarginContainer/Control/SubViewportContainer" index="0"]
scaling_3d_mode = 1
scaling_3d_scale = 1.25
fsr_sharpness = 0.4
size = Vector2i(2, 2)
render_target_update_mode = 0

[node name="Inspect" type="Node3D" parent="."]

[node name="inspect_camera" type="Camera3D" parent="Inspect"]
cull_mask = 1047568

[node name="PauseHandler" type="Node" parent="." node_paths=PackedStringArray("inventory_panel", "inv_item_parent")]
process_mode = 3
script = ExtResource("9_jj3jn")
inventory_panel = NodePath("")
inv_item_parent = NodePath("")

[editable path="Inventory/InventoryUI"]
