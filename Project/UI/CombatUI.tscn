[gd_scene load_steps=16 format=3 uid="uid://c7aquabrd8ye5"]

[ext_resource type="Script" uid="uid://dt3dmymyv36kc" path="res://Project/Systems/UI_Manager.gd" id="1_8n2ft"]
[ext_resource type="Texture2D" uid="uid://c151kbjp40esn" path="res://Project/UI/Sprites/noun-d-pad-1407690.png" id="1_vrpja"]
[ext_resource type="PackedScene" uid="uid://ce5o466jctpht" path="res://Project/UI/command.tscn" id="2_ycyim"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_73laj"]
bg_color = Color(0, 0, 0, 0.823529)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="LabelSettings" id="LabelSettings_uc74f"]
font_size = 32
font_color = Color(0.369609, 0.536953, 0.76277, 1)

[sub_resource type="LabelSettings" id="LabelSettings_bqxuj"]
font_size = 32

[sub_resource type="LabelSettings" id="LabelSettings_de6w5"]
font_size = 21

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uy1jg"]
bg_color = Color(0, 0, 0, 0.380392)
corner_radius_top_left = 1
corner_radius_top_right = 10
corner_radius_bottom_right = 1
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tia7r"]
bg_color = Color(0.535683, 0.727186, 0.751718, 1)
border_color = Color(1, 1, 1, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10
corner_detail = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t0b5w"]
bg_color = Color(0.37736, 0.2756, 0.53, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.52549)
corner_radius_top_left = 1
corner_radius_top_right = 10
corner_radius_bottom_right = 1
corner_radius_bottom_left = 10
corner_detail = 10

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_rbk1m"]
blend_mode = 1

[sub_resource type="Gradient" id="Gradient_hpipj"]
interpolation_color_space = 1
offsets = PackedFloat32Array(0.01, 1)
colors = PackedColorArray(0.834667, 0.68, 1, 0.505882, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_vr3fs"]
gradient = SubResource("Gradient_hpipj")
width = 128
height = 32
use_hdr = true
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.814024, 0.75)

[sub_resource type="LabelSettings" id="LabelSettings_at6hm"]
font_size = 64
shadow_size = 4
shadow_color = Color(0, 0, 0, 0.568627)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_eoih8"]
bg_color = Color(1, 1, 1, 0.0784314)
border_width_left = 5
border_width_right = 5
border_color = Color(1, 1, 1, 1)
corner_radius_top_left = 100
corner_radius_top_right = 100
corner_radius_bottom_right = 100
corner_radius_bottom_left = 100

[node name="CombatUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_8n2ft")
commandPrefab = ExtResource("2_ycyim")

[node name="LucidIndicator" type="PanelContainer" parent="."]
custom_minimum_size = Vector2(255, 40)
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 75.0
offset_top = -114.0
offset_right = 330.0
offset_bottom = -74.0
grow_vertical = 0
theme_override_styles/panel = SubResource("StyleBoxFlat_73laj")

[node name="HBoxContainer" type="HBoxContainer" parent="LucidIndicator"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TextureRect" type="TextureRect" parent="LucidIndicator/HBoxContainer"]
custom_minimum_size = Vector2(40, 40)
layout_mode = 2
texture = ExtResource("1_vrpja")
expand_mode = 5
stretch_mode = 4

[node name="Label" type="Label" parent="LucidIndicator/HBoxContainer"]
layout_mode = 2
text = "Lucid Mode"

[node name="LucidMode" type="PanelContainer" parent="."]
visible = false
custom_minimum_size = Vector2(500, 500)
layout_mode = 1
anchors_preset = -1
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 133.0
offset_top = 1025.0
offset_right = -2247.0
offset_bottom = -95.0
grow_vertical = 0

[node name="Label" type="Label" parent="LucidMode"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 0
text = "Lucid Mode"
label_settings = SubResource("LabelSettings_uc74f")
horizontal_alignment = 1

[node name="LucidModeBlock" type="PanelContainer" parent="LucidMode"]
custom_minimum_size = Vector2(500, 500)
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0

[node name="PanelContainer" type="PanelContainer" parent="LucidMode/LucidModeBlock"]
custom_minimum_size = Vector2(500, 75)
layout_mode = 2
size_flags_vertical = 0

[node name="Label" type="Label" parent="LucidMode/LucidModeBlock/PanelContainer"]
z_index = 1
layout_mode = 2
size_flags_vertical = 1
text = " 
  Spells"
label_settings = SubResource("LabelSettings_bqxuj")
vertical_alignment = 2

[node name="TargetGroup" type="PanelContainer" parent="LucidMode/LucidModeBlock"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 4

[node name="VBoxContainer" type="VBoxContainer" parent="LucidMode/LucidModeBlock/TargetGroup"]
z_index = 1
layout_mode = 2

[node name="Target" type="Panel" parent="LucidMode/LucidModeBlock/TargetGroup/VBoxContainer"]
custom_minimum_size = Vector2(300, 50)
layout_mode = 2

[node name="Label" type="Label" parent="LucidMode/LucidModeBlock/TargetGroup/VBoxContainer/Target"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 17.0
grow_horizontal = 2
grow_vertical = 2
text = "Target"
label_settings = SubResource("LabelSettings_de6w5")
vertical_alignment = 1

[node name="CommandGroup" type="PanelContainer" parent="LucidMode/LucidModeBlock/TargetGroup"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 4

[node name="VBoxContainer" type="VBoxContainer" parent="LucidMode/LucidModeBlock/TargetGroup/CommandGroup"]
z_index = 1
layout_mode = 2

[node name="Command" parent="LucidMode/LucidModeBlock/TargetGroup/CommandGroup/VBoxContainer" instance=ExtResource("2_ycyim")]
layout_mode = 2

[node name="CharStats" type="PanelContainer" parent="."]
custom_minimum_size = Vector2(600, 100)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -699.0
offset_top = -164.0
offset_right = -49.0
offset_bottom = -48.0
grow_horizontal = 0
grow_vertical = 0
theme_override_styles/panel = SubResource("StyleBoxFlat_73laj")

[node name="MarginContainer" type="MarginContainer" parent="CharStats"]
layout_mode = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 0

[node name="VBoxContainer" type="VBoxContainer" parent="CharStats/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="Name" type="Label" parent="CharStats/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Char Name"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Health" type="ProgressBar" parent="CharStats/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2
size_flags_vertical = 4
theme_override_styles/background = SubResource("StyleBoxFlat_uy1jg")
theme_override_styles/fill = SubResource("StyleBoxFlat_tia7r")
max_value = 200.0
step = 1.0
value = 150.0
show_percentage = false

[node name="Aura" type="ProgressBar" parent="CharStats/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2
theme_override_styles/background = SubResource("StyleBoxFlat_uy1jg")
theme_override_styles/fill = SubResource("StyleBoxFlat_t0b5w")
max_value = 2.0
step = 1.0
value = 1.0
show_percentage = false

[node name="HBoxContainer" type="HBoxContainer" parent="CharStats/MarginContainer/VBoxContainer/Aura"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -12.0
offset_bottom = 14.0
grow_horizontal = 2
grow_vertical = 2

[node name="AuraLeft" type="TextureRect" parent="CharStats/MarginContainer/VBoxContainer/Aura/HBoxContainer"]
self_modulate = Color(1, 1, 1, 0)
material = SubResource("CanvasItemMaterial_rbk1m")
layout_mode = 2
size_flags_horizontal = 3
texture = SubResource("GradientTexture2D_vr3fs")
expand_mode = 1

[node name="AuraRight" type="TextureRect" parent="CharStats/MarginContainer/VBoxContainer/Aura/HBoxContainer"]
self_modulate = Color(1, 1, 1, 0)
material = SubResource("CanvasItemMaterial_rbk1m")
layout_mode = 2
size_flags_horizontal = 3
texture = SubResource("GradientTexture2D_vr3fs")
expand_mode = 1

[node name="Label2" type="Label" parent="CharStats/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "AURA"
horizontal_alignment = 1

[node name="Control" type="Control" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -184.0
offset_top = -159.0
offset_right = -144.0
offset_bottom = -119.0
grow_horizontal = 2
grow_vertical = 0

[node name="BattleIndicator" type="Label" parent="Control"]
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Battle Mode"
label_settings = SubResource("LabelSettings_at6hm")

[node name="AimTarget" type="Panel" parent="."]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 70.0
offset_bottom = 40.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_eoih8")
