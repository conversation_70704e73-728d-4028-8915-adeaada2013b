[gd_scene load_steps=5 format=3 uid="uid://bpn5fdnfjplil"]

[ext_resource type="Theme" uid="uid://pvg1iq87niy7" path="res://Project/UI/new_theme.tres" id="1_78v2q"]
[ext_resource type="Script" uid="uid://m0doav47ru7y" path="res://Project/Systems/Inventory/InventorySlot.gd" id="2_dmeek"]

[sub_resource type="ButtonGroup" id="ButtonGroup_fw00b"]

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_wlkdc"]
size = Vector2(75, 75)

[node name="HeldSlot" type="Button"]
custom_minimum_size = Vector2(100, 150)
theme = ExtResource("1_78v2q")
theme_type_variation = &"FlatButton"
button_group = SubResource("ButtonGroup_fw00b")
text = "Item"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 0
expand_icon = true
script = ExtResource("2_dmeek")

[connection signal="button_down" from="." to="." method="_on_button_down"]
[connection signal="focus_entered" from="." to="." method="_on_focus_entered"]
[connection signal="focus_exited" from="." to="." method="_on_focus_exited"]
[connection signal="pressed" from="." to="." method="_on_pressed"]
