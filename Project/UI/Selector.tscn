[gd_scene load_steps=5 format=3 uid="uid://blelbivvkjf8q"]

[ext_resource type="Texture2D" uid="uid://e20iepwx7fut" path="res://Project/UI/Sprites/NVKnob.png" id="1_1y5oq"]
[ext_resource type="Script" uid="uid://ccnm27kqimndv" path="res://Project/Systems/Interaction/selector.gd" id="1_ukb5m"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5w7nj"]
bg_color = Color(0, 0, 0, 0.556863)
corner_radius_bottom_right = 30
corner_radius_bottom_left = 30

[sub_resource type="LabelSettings" id="LabelSettings_cyrsr"]

[node name="Selector" type="Control" node_paths=PackedStringArray("name_label", "description_label")]
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ukb5m")
name_label = NodePath("PanelContainer/Control/SelectorPanel/VBoxContainer/Label")
description_label = NodePath("PanelContainer/Control/SelectorPanel/VBoxContainer/Label2")

[node name="PanelContainer" type="PanelContainer" parent="."]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Control" type="Control" parent="PanelContainer"]
layout_mode = 2

[node name="SelectorPanel" type="Panel" parent="PanelContainer/Control"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_right = 100.0
offset_bottom = 100.0
grow_horizontal = 2
pivot_offset = Vector2(100, 0)
theme_override_styles/panel = SubResource("StyleBoxFlat_5w7nj")

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer/Control/SelectorPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 11.0
offset_bottom = -9.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(100, 0)

[node name="Label" type="Label" parent="PanelContainer/Control/SelectorPanel/VBoxContainer"]
custom_minimum_size = Vector2(200, 20)
layout_mode = 2
text = "Name Text"
label_settings = SubResource("LabelSettings_cyrsr")
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="Label2" type="Label" parent="PanelContainer/Control/SelectorPanel/VBoxContainer"]
custom_minimum_size = Vector2(200, 20)
layout_mode = 2
size_flags_vertical = 3
text = "Use Message Text"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="Control2" type="Control" parent="PanelContainer"]
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="PanelContainer/Control2"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -21.0
offset_bottom = -1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(10, 10)

[node name="TextureRect" type="TextureRect" parent="PanelContainer/Control2/PanelContainer"]
self_modulate = Color(1, 1, 1, 0.380392)
layout_mode = 2
texture = ExtResource("1_1y5oq")
expand_mode = 2
stretch_mode = 5
