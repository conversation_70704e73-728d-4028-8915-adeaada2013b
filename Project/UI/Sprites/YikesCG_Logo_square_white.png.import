[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dom5pxcsv4m1g"
path="res://.godot/imported/YikesCG_Logo_square_white.png-9020c8ca99149f5a52ef2e4c75f483df.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Project/UI/Sprites/YikesCG_Logo_square_white.png"
dest_files=["res://.godot/imported/YikesCG_Logo_square_white.png-9020c8ca99149f5a52ef2e4c75f483df.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
