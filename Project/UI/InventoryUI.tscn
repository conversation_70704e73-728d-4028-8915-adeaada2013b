[gd_scene load_steps=14 format=3 uid="uid://dyi2rn480l576"]

[ext_resource type="Shader" uid="uid://bxhawy5brbe3f" path="res://Project/Materials/2024 Materials/SHADERS/blur.gdshader" id="1_3kgun"]
[ext_resource type="Theme" uid="uid://pvg1iq87niy7" path="res://Project/UI/new_theme.tres" id="1_4xekl"]
[ext_resource type="StyleBox" uid="uid://bnanjn0l13rsf" path="res://Project/UI/new_style_box_flat.tres" id="2_mnqdt"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_fxhq8"]
shader = ExtResource("1_3kgun")
shader_parameter/lod = 1.99

[sub_resource type="Environment" id="Environment_sapkb"]

[sub_resource type="World3D" id="World3D_s0pkb"]
environment = SubResource("Environment_sapkb")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_xtbdo"]
metallic_specular = 0.82
roughness = 0.6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_unygm"]
bg_color = Color(0.6, 0.6, 0.6, 0)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="FontVariation" id="FontVariation_3mwcl"]
variation_embolden = 0.51

[sub_resource type="LabelSettings" id="LabelSettings_8pm8g"]
font = SubResource("FontVariation_3mwcl")
font_size = 32

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_trr7y"]
size = Vector2(500, 500)

[sub_resource type="LabelSettings" id="LabelSettings_8wiw0"]
font_size = 48

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_wlkdc"]
size = Vector2(75, 75)

[node name="UiTest" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -960.0
offset_bottom = -540.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
focus_mode = 2

[node name="Panel" type="Panel" parent="."]
material = SubResource("ShaderMaterial_fxhq8")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_constants/margin_left = 50
theme_override_constants/margin_top = 50
theme_override_constants/margin_right = 50
theme_override_constants/margin_bottom = 50

[node name="Control" type="Control" parent="MarginContainer"]
layout_mode = 2

[node name="SubViewportContainer" type="SubViewportContainer" parent="MarginContainer/Control"]
z_index = 1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 758.5
offset_right = -101.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
stretch = true

[node name="SubViewport" type="SubViewport" parent="MarginContainer/Control/SubViewportContainer"]
own_world_3d = true
world_3d = SubResource("World3D_s0pkb")
transparent_bg = true
handle_input_locally = false
canvas_cull_mask = 4293918729
size = Vector2i(2, 2)
render_target_update_mode = 4

[node name="Camera3D" type="Camera3D" parent="MarginContainer/Control/SubViewportContainer/SubViewport"]
cull_mask = 8

[node name="Node3D" type="Node3D" parent="MarginContainer/Control/SubViewportContainer/SubViewport/Camera3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -3.15079)

[node name="CSGSphere3D" type="CSGSphere3D" parent="MarginContainer/Control/SubViewportContainer/SubViewport/Camera3D/Node3D"]
layers = 8
radial_segments = 24
rings = 12
material = SubResource("StandardMaterial3D_xtbdo")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="MarginContainer/Control/SubViewportContainer/SubViewport"]
layers = 8
shadow_enabled = true
shadow_opacity = 0.61

[node name="EquipInfoPanelContainer" type="PanelContainer" parent="MarginContainer/Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 1485.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_unygm")

[node name="EquipInfoPanel" type="Panel" parent="MarginContainer/Control/EquipInfoPanelContainer"]
layout_mode = 2
theme_override_styles/panel = ExtResource("2_mnqdt")

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 25
theme_override_constants/margin_top = 50
theme_override_constants/margin_right = 25
theme_override_constants/margin_bottom = 100

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 25

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer/CenterContainer"]
layout_mode = 2
theme_override_constants/separation = 25

[node name="TitleLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer/CenterContainer/VBoxContainer"]
layout_mode = 2
text = "Equip Info
"
label_settings = SubResource("LabelSettings_8pm8g")
horizontal_alignment = 1
vertical_alignment = 1

[node name="DisplayImage" type="TextureRect" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer/CenterContainer/VBoxContainer"]
layout_mode = 2
texture = SubResource("PlaceholderTexture2D_trr7y")
expand_mode = 5

[node name="DescriptionLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Description"
label_settings = SubResource("LabelSettings_8pm8g")
vertical_alignment = 1

[node name="TypeLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Type"
label_settings = SubResource("LabelSettings_8pm8g")
vertical_alignment = 1

[node name="ClassLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Class"
label_settings = SubResource("LabelSettings_8pm8g")
vertical_alignment = 1

[node name="DamageLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Damage"
label_settings = SubResource("LabelSettings_8pm8g")
vertical_alignment = 1

[node name="SpellCostLabel" type="Label" parent="MarginContainer/Control/EquipInfoPanelContainer/EquipInfoPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Cost
"
label_settings = SubResource("LabelSettings_8pm8g")
vertical_alignment = 1

[node name="InventoryPanelContainer" type="PanelContainer" parent="MarginContainer/Control"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = -1
anchor_right = 0.545
anchor_bottom = 0.931
offset_top = 27.0
offset_right = 0.0999756
offset_bottom = 40.6199
grow_vertical = 2
mouse_filter = 1

[node name="InventoryPanel" type="Panel" parent="MarginContainer/Control/InventoryPanelContainer"]
layout_mode = 2
theme_override_styles/panel = ExtResource("2_mnqdt")

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -75.5
offset_top = -50.0
offset_right = 75.5
offset_bottom = -5.0
grow_horizontal = 2
mouse_filter = 2

[node name="InventoryLabel" type="Label" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer"]
layout_mode = 2
text = "Inventory"
label_settings = SubResource("LabelSettings_8pm8g")
horizontal_alignment = 1
vertical_alignment = 1

[node name="CenterContainer2" type="CenterContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Control" type="Control" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2"]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -496.0
offset_top = -490.0
offset_right = -39.0
offset_bottom = 490.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 50
theme_override_constants/margin_top = 50
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 50

[node name="HeldItemsPanel" type="Panel" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer/HeldItemsPanel"]
layout_mode = 1
anchors_preset = -1
anchor_right = 1.0
anchor_bottom = 0.071
offset_bottom = 4.52
grow_horizontal = 2
text = "Held Items"
label_settings = SubResource("LabelSettings_8wiw0")
horizontal_alignment = 1
vertical_alignment = 1

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer/HeldItemsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 71.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridContainer" type="GridContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer/HeldItemsPanel/CenterContainer"]
layout_mode = 2
theme_override_constants/h_separation = 25
theme_override_constants/v_separation = 50
columns = 3

[node name="MarginContainer2" type="MarginContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -39.0
offset_top = -490.0
offset_right = 496.0
offset_bottom = 490.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 50
theme_override_constants/margin_top = 50
theme_override_constants/margin_right = 50
theme_override_constants/margin_bottom = 50

[node name="EquipedPanel" type="Panel" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel"]
layout_mode = 2
anchor_right = 1.0
anchor_bottom = 0.076
offset_bottom = 0.120003
text = "Equipped"
label_settings = SubResource("LabelSettings_8wiw0")
horizontal_alignment = 1
vertical_alignment = 1

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 0
theme_override_constants/margin_top = 0
theme_override_constants/margin_right = 0
theme_override_constants/margin_bottom = 0

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer"]
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer"]
layout_mode = 2
theme_override_constants/separation = 100

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="Head" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Head"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="Upper" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Upper"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="Lower" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Lower"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="Shoes" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Shoes"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="VBoxContainer2" type="VBoxContainer" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 4
theme_override_constants/separation = 50

[node name="Acc1" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(80, 125)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Acc. 1"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="Hands" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(80, 125)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Hands"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true

[node name="Acc2" type="Button" parent="MarginContainer/Control/InventoryPanelContainer/InventoryPanel/CenterContainer2/Control/MarginContainer2/EquipedPanel/MarginContainer/CenterContainer/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(80, 125)
layout_mode = 2
theme = ExtResource("1_4xekl")
text = "Acc. 2"
icon = SubResource("PlaceholderTexture2D_wlkdc")
icon_alignment = 1
vertical_icon_alignment = 2
expand_icon = true
