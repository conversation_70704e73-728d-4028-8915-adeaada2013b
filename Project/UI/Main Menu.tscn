[gd_scene load_steps=6 format=3 uid="uid://cnkjvuqfd8a85"]

[ext_resource type="FontFile" uid="uid://beblq5j38n8hw" path="res://Project/UI/Fonts/smiley_sans/SmileySans-Oblique.otf" id="1_6wr07"]
[ext_resource type="Texture2D" uid="uid://csrkskyfdy75x" path="res://Project/UI/Sprites/Screenshot_2023-05-04_at_12.22.24_pm.png" id="1_ck1ig"]

[sub_resource type="FontVariation" id="FontVariation_yvkoc"]
base_font = ExtResource("1_6wr07")
variation_embolden = 0.09
spacing_glyph = 70

[sub_resource type="LabelSettings" id="LabelSettings_p8m74"]
font_size = 72
font_color = Color(0, 0, 0, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tbkue"]
bg_color = Color(0.111197, 0.111197, 0.111197, 1)

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Menu Buttons" type="PanelContainer" parent="."]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -148.0
offset_top = -13.0
offset_right = 148.0
offset_bottom = 211.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Menu Buttons"]
layout_mode = 2

[node name="PlayButton" type="Button" parent="Menu Buttons/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Play"
flat = true

[node name="ControlsButton" type="MenuButton" parent="Menu Buttons/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Controls"
item_count = 1
popup/item_0/text = "Hello"
popup/item_0/id = 0

[node name="StatementButton" type="MenuButton" parent="Menu Buttons/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Mission Statement"

[node name="SettingsButton" type="Button" parent="Menu Buttons/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Play"
flat = true

[node name="CreditsButton" type="MenuButton" parent="Menu Buttons/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Credits"

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -589.5
offset_top = -443.0
offset_right = 595.5
offset_bottom = -266.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.281487, 0.41268, 0.602454, 1)
theme_override_fonts/font = SubResource("FontVariation_yvkoc")
text = "ANGELSPHERE
_NIGHTSCAPE_"
label_settings = SubResource("LabelSettings_p8m74")
horizontal_alignment = 1
vertical_alignment = 1

[node name="Controls" type="Control" parent="."]
visible = false
layout_mode = 2
anchors_preset = 0
offset_left = 840.0
offset_top = 410.0
offset_right = 2040.0
offset_bottom = 1210.0

[node name="ControlsContainer" type="PanelContainer" parent="Controls"]
custom_minimum_size = Vector2(1200, 800)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -600.0
offset_top = -400.0
offset_right = 600.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_tbkue")

[node name="ControlsMargin" type="MarginContainer" parent="Controls/ControlsContainer"]
layout_mode = 2
theme_override_constants/margin_left = 100
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 100
theme_override_constants/margin_bottom = 100

[node name="TextureRect" type="TextureRect" parent="Controls/ControlsContainer/ControlsMargin"]
layout_mode = 2
texture = ExtResource("1_ck1ig")

[node name="Label" type="Label" parent="Controls"]
top_level = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.292
anchor_top = 0.253
anchor_right = 0.708
anchor_bottom = 0.747
offset_left = 553.04
offset_top = -59.86
offset_right = -579.04
offset_bottom = -837.14
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 32
text = "Controls"

[node name="MissionStatement" type="Control" parent="."]
visible = false
layout_mode = 2
anchors_preset = 0
offset_left = 840.0
offset_top = 410.0
offset_right = 2040.0
offset_bottom = 1210.0

[node name="StatementContainer" type="PanelContainer" parent="MissionStatement"]
custom_minimum_size = Vector2(1200, 800)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -600.0
offset_top = -400.0
offset_right = 600.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_tbkue")

[node name="StatementMargin" type="MarginContainer" parent="MissionStatement/StatementContainer"]
layout_mode = 2
theme_override_constants/margin_left = 100
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 100
theme_override_constants/margin_bottom = 100

[node name="Label" type="Label" parent="MissionStatement/StatementContainer/StatementMargin"]
layout_mode = 2
size_flags_vertical = 1
theme_override_font_sizes/font_size = 21
text = "
This is a piece of queer media by queer, disabled artists.

We have done our best to consult members of the queer, disabled and drag community through the process of making this demo.

Thank you for taking the time to consider our entry.

This demo does not reflect final gameplay. Many of the content decisions were incidental or placeholder as to hold space for systems testing. 

However, we have a clear picture of the final game and intentions to create it. 

With funding we aim to further develop the demo to be exhibited live.
"

[node name="Label" type="Label" parent="MissionStatement"]
top_level = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.292
anchor_top = 0.253
anchor_right = 0.708
anchor_bottom = 0.747
offset_left = 553.04
offset_top = -59.86
offset_right = -579.04
offset_bottom = -837.14
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 32
text = "Mission Statement"

[node name="Settings" type="Control" parent="."]
visible = false
layout_mode = 3
anchors_preset = 0
offset_left = 1680.0
offset_top = 820.0
offset_right = 2880.0
offset_bottom = 1620.0

[node name="ControlsContainer" type="PanelContainer" parent="Settings"]
custom_minimum_size = Vector2(1200, 800)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -600.0
offset_top = -400.0
offset_right = 600.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_tbkue")

[node name="ControlsMargin" type="MarginContainer" parent="Settings/ControlsContainer"]
layout_mode = 2
theme_override_constants/margin_left = 100
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 100
theme_override_constants/margin_bottom = 100

[node name="TextureRect" type="TextureRect" parent="Settings/ControlsContainer/ControlsMargin"]
layout_mode = 2
texture = ExtResource("1_ck1ig")

[node name="Label" type="Label" parent="Settings"]
top_level = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.292
anchor_top = 0.253
anchor_right = 0.708
anchor_bottom = 0.747
offset_left = 553.04
offset_top = -59.86
offset_right = -579.04
offset_bottom = -837.14
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 32
text = "Settings"

[node name="Credits" type="Control" parent="."]
visible = false
layout_mode = 2
anchors_preset = 0
offset_left = 840.0
offset_top = 410.0
offset_right = 2040.0
offset_bottom = 1210.0

[node name="CreditsContainer" type="PanelContainer" parent="Credits"]
custom_minimum_size = Vector2(1200, 800)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -600.0
offset_top = -400.0
offset_right = 600.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_tbkue")

[node name="CreditsMargin" type="MarginContainer" parent="Credits/CreditsContainer"]
layout_mode = 2
theme_override_constants/margin_left = 100
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 100
theme_override_constants/margin_bottom = 100

[node name="Label" type="Label" parent="Credits/CreditsContainer/CreditsMargin"]
layout_mode = 2
size_flags_vertical = 1
theme_override_font_sizes/font_size = 21
text = "
Created by Angas Smith

With help from:

Nory Gretz
Jamila Main
Cynthia Sobraty

Sound from ZapSplat"

[node name="Label" type="Label" parent="Credits"]
top_level = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.292
anchor_top = 0.253
anchor_right = 0.708
anchor_bottom = 0.747
offset_left = 553.04
offset_top = -59.86
offset_right = -579.04
offset_bottom = -837.14
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 32
text = "Credits"
