[gd_scene load_steps=24 format=3 uid="uid://b17632syn4fco"]

[ext_resource type="Script" uid="uid://dfgok1v16gbbv" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_layer.gd" id="1_6331q"]
[ext_resource type="Script" uid="uid://be4svkc2co5h6" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/animations.gd" id="2_hhh3a"]
[ext_resource type="StyleBox" uid="uid://dkv1pl1c1dq6" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_default_panel.tres" id="3_t2pfg"]
[ext_resource type="Script" uid="uid://cwwudjmkt1tj8" path="res://addons/dialogic/Modules/Text/node_dialog_text.gd" id="4_6dw70"]
[ext_resource type="Script" uid="uid://h0fdst6mhn77" path="res://addons/dialogic/Modules/Text/node_type_sound.gd" id="5_4ichr"]
[ext_resource type="Script" uid="uid://caan1vj5wknxw" path="res://addons/dialogic/Modules/Text/node_next_indicator.gd" id="6_vsfqj"]
[ext_resource type="Texture2D" uid="uid://b0rpqfg4fhebk" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/next.svg" id="7_772y1"]
[ext_resource type="Script" uid="uid://1sh212ald0ul" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/autoadvance_indicator.gd" id="8_ppc6k"]
[ext_resource type="StyleBox" uid="uid://m7gyepkysu83" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_name_label_panel.tres" id="9_artox"]
[ext_resource type="Script" uid="uid://dkbyl2mih0u0r" path="res://addons/dialogic/Modules/Text/node_name_label.gd" id="10_o2f5l"]
[ext_resource type="Script" uid="uid://cew58lj10tddl" path="res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Choices/vn_choice_layer.gd" id="11_y5m3f"]
[ext_resource type="Script" uid="uid://cb4hawhwcq5ov" path="res://addons/dialogic/Modules/Choice/node_choice_button.gd" id="12_3av5u"]
[ext_resource type="Script" uid="uid://vyrf7cf2bwvl" path="res://addons/dialogic/Modules/Choice/node_button_sound.gd" id="13_sq8y1"]
[ext_resource type="AudioStream" uid="uid://b6c1p14bc20p1" path="res://addons/dialogic/Example Assets/sound-effects/typing1.wav" id="14_jm203"]
[ext_resource type="AudioStream" uid="uid://c2viukvbub6v6" path="res://addons/dialogic/Example Assets/sound-effects/typing4.wav" id="15_mxg47"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1hyq8"]
bg_color = Color(0.439049, 0.439049, 0.439049, 1)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="Animation" id="Animation_au0a2"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/AnimationParent:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Anchor/AnimationParent:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Anchor/AnimationParent:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Anchor/AnimationParent:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}
tracks/4/type = "bezier"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Anchor/AnimationParent/Sizer/DialogTextPanel:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="Animation" id="Animation_6kbwc"]
resource_name = "new_text"
length = 0.4
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/AnimationParent/Sizer/DialogTextPanel:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(3, 3, 3, 3, 3),
"points": PackedFloat32Array(0, -0.025, 0, 0.025, 0, 0.005, -0.025, 0, 0.025, 0, -0.005, -0.025, 0, 0.025, 0, 0.005, -0.025, 0, 0.025, 0, 0, -0.025, 0, 0.025, 0),
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4)
}

[sub_resource type="Animation" id="Animation_g6k55"]
resource_name = "textbox_fade_up"
length = 0.7
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/AnimationParent:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.7),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, 50), Vector2(0, 19.6793), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Anchor/AnimationParent:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.1, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Anchor/AnimationParent:rotation")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Anchor/AnimationParent:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_htbgc"]
resource_name = "textbox_pop"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/AnimationParent:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Anchor/AnimationParent:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [-0.0899883, 0.0258223, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Anchor/AnimationParent:scale")
tracks/2/interp = 2
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.793957, 0.778082), Vector2(0.937299, 1.14248), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Anchor/AnimationParent:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_c14kh"]
_data = {
&"RESET": SubResource("Animation_au0a2"),
&"new_text": SubResource("Animation_6kbwc"),
&"textbox_fade_up": SubResource("Animation_g6k55"),
&"textbox_pop": SubResource("Animation_htbgc")
}

[sub_resource type="FontVariation" id="FontVariation_v8y64"]

[sub_resource type="AudioStream" id="AudioStream_pe27w"]

[node name="SmsUi" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -211.0
offset_top = -414.0
offset_right = 211.0
offset_bottom = 414.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1hyq8")

[node name="MarginContainer" type="MarginContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Panel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="VN_TextboxLayer" type="Control" parent="Panel/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
mouse_filter = 2
script = ExtResource("1_6331q")
box_panel = "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_default_panel.tres"
box_size = Vector2(550, 150)
name_label_box_panel = "res://addons/dialogic/Modules/DefaultLayoutParts/Layer_VN_Textbox/vn_textbox_name_label_panel.tres"
name_label_box_modulate = Color(0, 0, 0, 1)

[node name="Animations" type="AnimationPlayer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer"]
unique_name_in_owner = true
libraries = {
&"": SubResource("AnimationLibrary_c14kh")
}
autoplay = "RESET"
script = ExtResource("2_hhh3a")

[node name="Anchor" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer"]
layout_mode = 1
anchor_right = 1.0
anchor_bottom = 0.118
offset_bottom = 0.29599
grow_horizontal = 2

[node name="AnimationParent" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Sizer" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -150.0
offset_top = -50.0
offset_right = 150.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2

[node name="DialogTextPanel" type="PanelContainer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer"]
unique_name_in_owner = true
self_modulate = Color(0.00784314, 0.00784314, 0.00784314, 0.843137)
custom_minimum_size = Vector2(300, 50)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = ExtResource("3_t2pfg")
metadata/_edit_layout_mode = 1

[node name="DialogicNode_DialogText" type="RichTextLabel" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel" node_paths=PackedStringArray("textbox_root")]
unique_name_in_owner = true
layout_mode = 2
mouse_filter = 1
theme_override_colors/default_color = Color(1, 1, 1, 1)
theme_override_font_sizes/bold_italics_font_size = 15
theme_override_font_sizes/italics_font_size = 15
theme_override_font_sizes/normal_font_size = 15
theme_override_font_sizes/bold_font_size = 15
bbcode_enabled = true
text = "Some default text"
visible_characters_behavior = 1
script = ExtResource("4_6dw70")
textbox_root = NodePath("..")

[node name="DialogicNode_TypeSounds" type="AudioStreamPlayer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel/DialogicNode_DialogText"]
unique_name_in_owner = true
script = ExtResource("5_4ichr")
play_every_character = 0

[node name="NextIndicator" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 8
mouse_filter = 2
script = ExtResource("6_vsfqj")
show_on_questions = true
texture = ExtResource("7_772y1")
metadata/_edit_layout_mode = 1

[node name="AutoAdvanceProgressbar" type="ProgressBar" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel"]
unique_name_in_owner = true
modulate = Color(1, 1, 1, 0.188235)
custom_minimum_size = Vector2(0, 10)
layout_mode = 2
size_flags_vertical = 8
mouse_filter = 2
max_value = 1.0
step = 0.001
value = 0.5
show_percentage = false
script = ExtResource("8_ppc6k")

[node name="NameLabelHolder" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel"]
layout_mode = 2
mouse_filter = 2

[node name="NameLabelPanel" type="PanelContainer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel/NameLabelHolder"]
unique_name_in_owner = true
self_modulate = Color(0.00784314, 0.00784314, 0.00784314, 0.843137)
layout_mode = 1
offset_top = -50.0
offset_right = 9.0
offset_bottom = -25.0
mouse_filter = 2
theme_override_styles/panel = ExtResource("9_artox")
metadata/_edit_layout_mode = 1
metadata/_edit_use_custom_anchors = true
metadata/_edit_group_ = true

[node name="DialogicNode_NameLabel" type="Label" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/Anchor/AnimationParent/Sizer/DialogTextPanel/NameLabelHolder/NameLabelPanel" node_paths=PackedStringArray("name_label_root")]
unique_name_in_owner = true
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = SubResource("FontVariation_v8y64")
theme_override_font_sizes/font_size = 15
text = "S"
script = ExtResource("10_o2f5l")
name_label_root = NodePath("..")

[node name="VN_ChoiceLayer" type="Control" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer"]
layout_mode = 1
anchor_top = 0.413
anchor_right = 1.0
anchor_bottom = 0.494
offset_top = -229.964
offset_bottom = -230.032
grow_horizontal = 2
size_flags_vertical = 3
mouse_filter = 2
script = ExtResource("11_y5m3f")

[node name="DialogicNode_ButtonSound" type="AudioStreamPlayer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer"]
unique_name_in_owner = true
script = ExtResource("13_sq8y1")
sound_pressed = ExtResource("14_jm203")
sound_hover = ExtResource("15_mxg47")
sound_focus = SubResource("AudioStream_pe27w")

[node name="GridContainer" type="GridContainer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 51.0
offset_right = -15.0
offset_bottom = 42.0
grow_horizontal = 2
grow_vertical = 2
columns = 4

[node name="DialogicNode_ChoiceButton1" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton2" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton3" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton4" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton5" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton6" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton7" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton8" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton9" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton10" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="DialogicNode_ChoiceButton11" type="Button" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer/GridContainer"]
layout_mode = 2
text = "Some text"
script = ExtResource("12_3av5u")

[node name="Choices" type="HBoxContainer" parent="Panel/MarginContainer/VBoxContainer/VN_TextboxLayer/VN_ChoiceLayer"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_top = -15.5
offset_bottom = 15.5
grow_horizontal = 2
grow_vertical = 2
