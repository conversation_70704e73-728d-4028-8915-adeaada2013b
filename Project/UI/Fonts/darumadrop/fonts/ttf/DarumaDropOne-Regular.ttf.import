[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://m2v12mi6qwgl"
path="res://.godot/imported/DarumaDropOne-Regular.ttf-25fb6db8c294c61cfc56877344fb5bf4.fontdata"

[deps]

source_file="res://Project/UI/Fonts/darumadrop/fonts/ttf/DarumaDropOne-Regular.ttf"
dest_files=["res://.godot/imported/DarumaDropOne-Regular.ttf-25fb6db8c294c61cfc56877344fb5bf4.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
