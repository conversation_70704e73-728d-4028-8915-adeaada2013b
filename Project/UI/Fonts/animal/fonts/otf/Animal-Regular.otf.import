[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://cpg02ifv83nvh"
path="res://.godot/imported/Animal-Regular.otf-5c767d3f74fe4243cd430b9cce23e87f.fontdata"

[deps]

source_file="res://Project/UI/Fonts/animal/fonts/otf/Animal-Regular.otf"
dest_files=["res://.godot/imported/Animal-Regular.otf-5c767d3f74fe4243cd430b9cce23e87f.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
