extends CharacterBody3D
class_name Enemy

@export var nav_agent: NavigationAgent3D

var current

func _ready() -> void:
	var map = get_world_3d().get_navigation_map()
	set_nav_map(map)

func set_nav_map(map: RID):
	nav_agent.set_navigation_map(map)
	NavigationServer3D.map_force_update(map)

func _physics_process(_delta: float) -> void:
	move_and_slide()

func update_target_position(target_position):
	nav_agent.target_position = target_position

func play_animation(_animation_name: String):
	pass
